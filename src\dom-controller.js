const puppeteer = require('puppeteer')
const path = require('path')
const fs = require('fs')
const logger = require('./utils/logger')
const config = require('./config')
const SessionManager = require('./session-manager')

class DOMController {
	constructor() {
		this.browser = null
		this.page = null
		this.isInitialized = false
		this.currentAsset = null
		this.currentTimeframe = null
		this.currentAmount = config.trading.defaultAmount
		this.sessionManager = new SessionManager()
	}

	async initialize() {
		try {
			logger.info('Initializing browser with session persistence')

			// Create user data directory if it doesn't exist
			const userDataDir = path.resolve(config.browser.userDataDir)
			if (!fs.existsSync(userDataDir)) {
				fs.mkdirSync(userDataDir, { recursive: true })
				logger.info('Created browser data directory', { userDataDir })
			}

			const launchOptions = {
				headless: config.browser.headless,
				devtools: config.browser.devtools,
				slowMo: config.browser.slowMo,
				args: [
					'--no-sandbox',
					'--disable-setuid-sandbox',
					'--disable-dev-shm-usage',
					'--disable-accelerated-2d-canvas',
					'--no-first-run',
					'--no-zygote',
					'--disable-gpu',
					'--disable-web-security',
					'--disable-features=VizDisplayCompositor'
				]
			}

			// Add user data directory for session persistence
			if (config.browser.sessionPersistence) {
				launchOptions.userDataDir = userDataDir
				logger.info('Session persistence enabled', { userDataDir })
			}

			this.browser = await puppeteer.launch(launchOptions)
			this.page = await this.browser.newPage()
			await this.page.setViewport(config.browser.viewport)

			// Set user agent to avoid detection
			await this.page.setUserAgent(
				'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
			)

			// Set additional headers to appear more like a real browser
			await this.page.setExtraHTTPHeaders({
				'Accept-Language': 'en-US,en;q=0.9',
				'Accept-Encoding': 'gzip, deflate, br',
				Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8'
			})

			logger.info('Browser initialized successfully')
			this.isInitialized = true
			return true
		} catch (error) {
			logger.error('Failed to initialize browser', error)
			return false
		}
	}

	async navigateToTradingPage() {
		if (!this.isInitialized) {
			throw new Error('Browser not initialized')
		}

		try {
			logger.info('Navigating to trading page')
			await this.page.goto(config.pocketOption.url, {
				waitUntil: 'networkidle2',
				timeout: 30000
			})

			// Wait for the trading interface to load
			await this.page.waitForSelector('input[type="text"]', { timeout: 10000 })

			logger.info('Successfully navigated to trading page')
			return true
		} catch (error) {
			logger.error('Failed to navigate to trading page', error)
			return false
		}
	}

	async switchToDemo() {
		try {
			logger.info('Switching to demo account')

			// Look for demo account indicator or switch
			const demoText = await this.page.$eval('body', el => el.textContent)

			if (demoText.includes('Demo')) {
				logger.info('Already on demo account')
				return true
			}

			// Try to find and click demo switch (this selector may need adjustment)
			const demoSwitch = await this.page.$('[data-account-type="demo"]')
			if (demoSwitch) {
				await demoSwitch.click()
				await this.page.waitForTimeout(2000)
				logger.info('Switched to demo account')
				return true
			}

			logger.warn('Demo switch not found, assuming already on demo')
			return true
		} catch (error) {
			logger.error('Failed to switch to demo account', error)
			return false
		}
	}

	async selectAsset(asset) {
		try {
			logger.info('Selecting asset', { asset })

			// Click on the asset selector (this may need adjustment based on actual DOM)
			const assetSelector = await this.page.$('.asset-selector, [data-asset], .current-asset')
			if (assetSelector) {
				await assetSelector.click()
				await this.page.waitForTimeout(1000)

				// Look for the specific asset in the dropdown
				const assetOption = await this.page.$(`[data-asset="${asset}"], [title="${asset}"]`)
				if (assetOption) {
					await assetOption.click()
					await this.page.waitForTimeout(1000)
					this.currentAsset = asset
					logger.info('Asset selected successfully', { asset })
					return true
				}
			}

			logger.warn('Asset selector or option not found', { asset })
			return false
		} catch (error) {
			logger.error('Failed to select asset', error)
			return false
		}
	}

	async selectTimeframe(timeframe) {
		try {
			logger.info('Selecting timeframe', { timeframe })

			// Find the timeframe dropdown
			const timeframeSelector = await this.page.$('select, .timeframe-selector')
			if (timeframeSelector) {
				await timeframeSelector.click()
				await this.page.waitForTimeout(500)

				// Select the specific timeframe
				await this.page.select('select', timeframe)
				await this.page.waitForTimeout(1000)

				this.currentTimeframe = timeframe
				logger.info('Timeframe selected successfully', { timeframe })
				return true
			}

			logger.warn('Timeframe selector not found')
			return false
		} catch (error) {
			logger.error('Failed to select timeframe', error)
			return false
		}
	}

	async setTradeAmount(amount) {
		try {
			logger.info('Setting trade amount', { amount })

			// Find the amount input field
			const amountInput = await this.page.$('input[type="text"], .amount-input, [data-amount]')
			if (amountInput) {
				// Clear existing value and set new amount
				await amountInput.click({ clickCount: 3 }) // Select all text
				await amountInput.type(amount.toString())
				await this.page.waitForTimeout(500)

				this.currentAmount = amount
				logger.info('Trade amount set successfully', { amount })
				return true
			}

			logger.warn('Amount input field not found')
			return false
		} catch (error) {
			logger.error('Failed to set trade amount', error)
			return false
		}
	}

	async executeTrade(direction) {
		try {
			logger.info('Executing trade', { direction, amount: this.currentAmount, asset: this.currentAsset })

			const buttonSelector =
				direction === 'buy'
					? '.buy-button, [data-direction="buy"], button:contains("Buy")'
					: '.sell-button, [data-direction="sell"], button:contains("Sell")'

			const tradeButton = await this.page.$(buttonSelector)
			if (tradeButton) {
				await tradeButton.click()
				await this.page.waitForTimeout(1000)

				logger.trade('Trade executed', {
					direction,
					amount: this.currentAmount,
					asset: this.currentAsset,
					timeframe: this.currentTimeframe,
					timestamp: new Date().toISOString()
				})

				return true
			}

			logger.warn('Trade button not found', { direction })
			return false
		} catch (error) {
			logger.error('Failed to execute trade', error)
			return false
		}
	}

	async getBalance() {
		try {
			const balanceElement = await this.page.$('.balance, [data-balance], .account-balance')
			if (balanceElement) {
				const balanceText = await balanceElement.textContent()
				const balance = parseFloat(balanceText.replace(/[^\d.]/g, ''))
				return balance
			}
			return null
		} catch (error) {
			logger.error('Failed to get balance', error)
			return null
		}
	}

	async getCurrentPrice() {
		try {
			const priceElement = await this.page.$('.current-price, [data-price], .price-display')
			if (priceElement) {
				const priceText = await priceElement.textContent()
				const price = parseFloat(priceText.replace(/[^\d.]/g, ''))
				return price
			}
			return null
		} catch (error) {
			logger.error('Failed to get current price', error)
			return null
		}
	}

	async getOpenTrades() {
		try {
			const trades = []
			const tradeElements = await this.page.$$('.open-trade, [data-trade-id]')

			for (const element of tradeElements) {
				const tradeData = await element.evaluate(el => ({
					id: el.dataset.tradeId || el.id,
					asset: el.querySelector('.trade-asset')?.textContent,
					amount: el.querySelector('.trade-amount')?.textContent,
					direction: el.querySelector('.trade-direction')?.textContent,
					profit: el.querySelector('.trade-profit')?.textContent
				}))

				trades.push(tradeData)
			}

			return trades
		} catch (error) {
			logger.error('Failed to get open trades', error)
			return []
		}
	}

	async takeScreenshot(filename) {
		try {
			if (!this.page) return false

			await this.page.screenshot({
				path: `./screenshots/${filename}`,
				fullPage: true
			})

			logger.info('Screenshot taken', { filename })
			return true
		} catch (error) {
			logger.error('Failed to take screenshot', error)
			return false
		}
	}

	async close() {
		try {
			if (this.browser) {
				await this.browser.close()
				logger.info('Browser closed')
			}
		} catch (error) {
			logger.error('Failed to close browser', error)
		}
	}

	// Utility method to wait for element
	async waitForElement(selector, timeout = 5000) {
		try {
			await this.page.waitForSelector(selector, { timeout })
			return true
		} catch (error) {
			logger.warn('Element not found within timeout', { selector, timeout })
			return false
		}
	}

	// Method to inject custom JavaScript for WebSocket monitoring
	async injectWebSocketMonitor() {
		try {
			await this.page.evaluateOnNewDocument(() => {
				const originalWebSocket = window.WebSocket
				window.WebSocket = function (...args) {
					const ws = new originalWebSocket(...args)

					// Store WebSocket URL for the bot to access
					window.pocketOptionWebSocketUrl = args[0]

					// Monitor messages
					const originalSend = ws.send
					ws.send = function (data) {
						window.lastWebSocketSent = data
						return originalSend.call(this, data)
					}

					ws.addEventListener('message', event => {
						window.lastWebSocketReceived = event.data
					})

					return ws
				}
			})

			logger.info('WebSocket monitor injected')
			return true
		} catch (error) {
			logger.error('Failed to inject WebSocket monitor', error)
			return false
		}
	}

	async getWebSocketUrl() {
		try {
			const wsUrl = await this.page.evaluate(() => window.pocketOptionWebSocketUrl)
			return wsUrl
		} catch (error) {
			logger.error('Failed to get WebSocket URL', error)
			return null
		}
	}

	// Session management methods
	async isLoggedIn() {
		try {
			// Check for login indicators (adjust selectors based on actual Pocket Option DOM)
			const loginIndicators = ['.user-info', '.account-balance', '[data-user]', '.logout-button', '.profile-menu']

			for (const selector of loginIndicators) {
				const element = await this.page.$(selector)
				if (element) {
					logger.info('User appears to be logged in', { indicator: selector })
					return true
				}
			}

			// Check if we're on login page
			const currentUrl = this.page.url()
			if (currentUrl.includes('login') || currentUrl.includes('auth')) {
				logger.info('On login page, user not logged in')
				return false
			}

			// Check for demo account text as another indicator
			const bodyText = await this.page.$eval('body', el => el.textContent).catch(() => '')
			if (bodyText.includes('Demo') && bodyText.includes('USD')) {
				logger.info('Demo account detected, user appears logged in')
				return true
			}

			logger.warn('Unable to determine login status')
			return false
		} catch (error) {
			logger.error('Failed to check login status', error)
			return false
		}
	}

	async waitForLogin(timeout = 300000) {
		// 5 minutes timeout for manual login
		try {
			logger.info('Waiting for user to log in manually...', { timeout: timeout / 1000 + 's' })

			const startTime = Date.now()
			while (Date.now() - startTime < timeout) {
				if (await this.isLoggedIn()) {
					logger.info('User successfully logged in')
					return true
				}

				// Wait 2 seconds before checking again
				await new Promise(resolve => setTimeout(resolve, 2000))
			}

			logger.warn('Login timeout reached')
			return false
		} catch (error) {
			logger.error('Error while waiting for login', error)
			return false
		}
	}

	async saveSession() {
		try {
			if (!this.page) return false

			// Save cookies
			const cookies = await this.page.cookies()
			const sessionData = {
				cookies,
				url: this.page.url(),
				timestamp: Date.now()
			}

			const sessionFile = path.join(config.browser.userDataDir, 'session.json')
			fs.writeFileSync(sessionFile, JSON.stringify(sessionData, null, 2))

			logger.info('Session saved successfully', { cookieCount: cookies.length })
			return true
		} catch (error) {
			logger.error('Failed to save session', error)
			return false
		}
	}

	async loadSession() {
		try {
			const sessionFile = path.join(config.browser.userDataDir, 'session.json')

			if (!fs.existsSync(sessionFile)) {
				logger.info('No saved session found')
				return false
			}

			const sessionData = JSON.parse(fs.readFileSync(sessionFile, 'utf8'))

			// Check if session is not too old (24 hours)
			const sessionAge = Date.now() - sessionData.timestamp
			const maxAge = 24 * 60 * 60 * 1000 // 24 hours
			if (sessionAge > maxAge) {
				logger.info('Saved session is too old, ignoring')
				return false
			}

			// Set cookies
			if (sessionData.cookies && sessionData.cookies.length > 0) {
				await this.page.setCookie(...sessionData.cookies)
				logger.info('Session loaded successfully', { cookieCount: sessionData.cookies.length })
				return true
			}

			return false
		} catch (error) {
			logger.error('Failed to load session', error)
			return false
		}
	}

	async handleLogin() {
		try {
			logger.info('Handling login process')

			// Check if we have a valid saved session
			const sessionInfo = this.sessionManager.getSessionInfo()
			if (sessionInfo) {
				logger.info('Found existing session', {
					age: sessionInfo.ageMinutes + ' minutes',
					isValid: sessionInfo.isValid
				})
			}

			// First, try to load existing session
			const sessionLoaded = await this.sessionManager.loadSession(this.page)

			// If session was loaded, check if we're logged in
			if (sessionLoaded && (await this.isLoggedIn())) {
				logger.info('Successfully restored session - already logged in')
				return true
			}

			// Navigate to the trading page if not already there
			const currentUrl = this.page.url()
			if (!currentUrl.includes('pocketoption.com')) {
				await this.navigateToTradingPage()
			}

			// Check if already logged in (maybe session worked)
			if (await this.isLoggedIn()) {
				logger.info('Already logged in')
				// Save current session
				await this.sessionManager.saveSession(this.page)
				return true
			}

			// If not logged in, wait for manual login
			logger.info('Please log in manually in the browser window')
			logger.info('The bot will wait for up to 5 minutes for you to complete login')

			const loginSuccess = await this.waitForLogin()

			if (loginSuccess) {
				// Save the session for future use
				await this.sessionManager.saveSession(this.page)
				logger.info('Login successful and session saved')
				return true
			}

			logger.error('Login failed or timed out')
			return false
		} catch (error) {
			logger.error('Failed to handle login', error)
			return false
		}
	}
}

module.exports = DOMController
