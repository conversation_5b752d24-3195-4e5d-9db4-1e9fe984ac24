require('dotenv').config()

const config = {
	// Pocket Option Settings
	pocketOption: {
		url: 'https://pocketoption.com/en/cabinet/demo-quick-high-low/',
		loginUrl: 'https://pocketoption.com/en/login/',
		websocketUrl: 'wss://pocketoption.com/ws/' // This will need to be discovered
	},

	// Trading Settings
	trading: {
		defaultAmount: 1,
		maxAmount: 100,
		minAmount: 1,
		defaultAsset: 'AUD/CAD OTC',
		defaultTimeframe: 'M8', // M8, M10, M30, H1, H4, D1, D7, D14
		maxConcurrentTrades: 3,
		riskPercentage: 2 // Percentage of balance to risk per trade
	},

	// Strategy Settings
	strategy: {
		type: 'trend_following', // trend_following, mean_reversion, scalping
		indicators: {
			movingAverage: {
				period: 20,
				type: 'EMA' // SMA, EMA
			},
			rsi: {
				period: 14,
				overbought: 70,
				oversold: 30
			},
			bollinger: {
				period: 20,
				stdDev: 2
			}
		}
	},

	// Risk Management
	riskManagement: {
		maxDailyLoss: 50, // Maximum daily loss in USD
		maxConsecutiveLosses: 3,
		stopLossPercentage: 10,
		takeProfitPercentage: 20,
		martingaleEnabled: false,
		martingaleMultiplier: 2
	},

	// Browser Settings
	browser: {
		headless: false, // Set to true for production
		devtools: true,
		slowMo: 100, // Slow down actions for debugging
		viewport: {
			width: 1920,
			height: 1080
		},
		userDataDir: './browser-data', // Persistent browser data directory
		sessionPersistence: true, // Enable session persistence
		profileName: 'pocket-option-bot' // Browser profile name
	},

	// Logging
	logging: {
		level: 'info', // error, warn, info, debug
		logToFile: true,
		logDirectory: './logs'
	},

	// WebSocket Settings
	websocket: {
		reconnectInterval: 5000,
		maxReconnectAttempts: 10,
		pingInterval: 30000
	}
}

module.exports = config
