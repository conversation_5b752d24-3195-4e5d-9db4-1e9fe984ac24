const fs = require('fs');
const path = require('path');
const logger = require('./utils/logger');
const config = require('./config');

class SessionManager {
  constructor() {
    this.sessionDir = path.resolve(config.browser.userDataDir);
    this.sessionFile = path.join(this.sessionDir, 'session.json');
    this.cookiesFile = path.join(this.sessionDir, 'cookies.json');
  }

  // Ensure session directory exists
  ensureSessionDirectory() {
    if (!fs.existsSync(this.sessionDir)) {
      fs.mkdirSync(this.sessionDir, { recursive: true });
      logger.info('Created session directory', { path: this.sessionDir });
    }
  }

  // Save session data including cookies and local storage
  async saveSession(page) {
    try {
      this.ensureSessionDirectory();

      // Get cookies
      const cookies = await page.cookies();
      
      // Get local storage data
      const localStorage = await page.evaluate(() => {
        const data = {};
        for (let i = 0; i < window.localStorage.length; i++) {
          const key = window.localStorage.key(i);
          data[key] = window.localStorage.getItem(key);
        }
        return data;
      });

      // Get session storage data
      const sessionStorage = await page.evaluate(() => {
        const data = {};
        for (let i = 0; i < window.sessionStorage.length; i++) {
          const key = window.sessionStorage.key(i);
          data[key] = window.sessionStorage.getItem(key);
        }
        return data;
      });

      const sessionData = {
        cookies,
        localStorage,
        sessionStorage,
        url: page.url(),
        userAgent: await page.evaluate(() => navigator.userAgent),
        timestamp: Date.now(),
        version: '1.0'
      };

      // Save session data
      fs.writeFileSync(this.sessionFile, JSON.stringify(sessionData, null, 2));
      
      logger.info('Session saved successfully', {
        cookieCount: cookies.length,
        localStorageKeys: Object.keys(localStorage).length,
        sessionStorageKeys: Object.keys(sessionStorage).length,
        url: sessionData.url
      });

      return true;
    } catch (error) {
      logger.error('Failed to save session', error);
      return false;
    }
  }

  // Load session data and restore cookies and storage
  async loadSession(page) {
    try {
      if (!fs.existsSync(this.sessionFile)) {
        logger.info('No saved session found');
        return false;
      }

      const sessionData = JSON.parse(fs.readFileSync(this.sessionFile, 'utf8'));

      // Check session age (24 hours max)
      const sessionAge = Date.now() - sessionData.timestamp;
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      
      if (sessionAge > maxAge) {
        logger.info('Session expired, removing old session file');
        this.clearSession();
        return false;
      }

      // Set cookies
      if (sessionData.cookies && sessionData.cookies.length > 0) {
        await page.setCookie(...sessionData.cookies);
      }

      // Navigate to the saved URL first
      if (sessionData.url) {
        await page.goto(sessionData.url, { waitUntil: 'networkidle2' });
      }

      // Restore local storage
      if (sessionData.localStorage) {
        await page.evaluate((data) => {
          for (const [key, value] of Object.entries(data)) {
            window.localStorage.setItem(key, value);
          }
        }, sessionData.localStorage);
      }

      // Restore session storage
      if (sessionData.sessionStorage) {
        await page.evaluate((data) => {
          for (const [key, value] of Object.entries(data)) {
            window.sessionStorage.setItem(key, value);
          }
        }, sessionData.sessionStorage);
      }

      logger.info('Session loaded successfully', {
        age: Math.round(sessionAge / 1000 / 60) + ' minutes',
        cookieCount: sessionData.cookies?.length || 0,
        url: sessionData.url
      });

      return true;
    } catch (error) {
      logger.error('Failed to load session', error);
      return false;
    }
  }

  // Check if session exists and is valid
  hasValidSession() {
    try {
      if (!fs.existsSync(this.sessionFile)) {
        return false;
      }

      const sessionData = JSON.parse(fs.readFileSync(this.sessionFile, 'utf8'));
      const sessionAge = Date.now() - sessionData.timestamp;
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours

      return sessionAge <= maxAge;
    } catch (error) {
      logger.error('Error checking session validity', error);
      return false;
    }
  }

  // Clear saved session
  clearSession() {
    try {
      if (fs.existsSync(this.sessionFile)) {
        fs.unlinkSync(this.sessionFile);
        logger.info('Session file cleared');
      }
      
      if (fs.existsSync(this.cookiesFile)) {
        fs.unlinkSync(this.cookiesFile);
        logger.info('Cookies file cleared');
      }

      return true;
    } catch (error) {
      logger.error('Failed to clear session', error);
      return false;
    }
  }

  // Get session info
  getSessionInfo() {
    try {
      if (!fs.existsSync(this.sessionFile)) {
        return null;
      }

      const sessionData = JSON.parse(fs.readFileSync(this.sessionFile, 'utf8'));
      const sessionAge = Date.now() - sessionData.timestamp;

      return {
        exists: true,
        age: sessionAge,
        ageMinutes: Math.round(sessionAge / 1000 / 60),
        ageHours: Math.round(sessionAge / 1000 / 60 / 60),
        url: sessionData.url,
        cookieCount: sessionData.cookies?.length || 0,
        timestamp: new Date(sessionData.timestamp).toISOString(),
        isValid: sessionAge <= 24 * 60 * 60 * 1000
      };
    } catch (error) {
      logger.error('Error getting session info', error);
      return null;
    }
  }

  // Export session for backup
  exportSession(exportPath) {
    try {
      if (!fs.existsSync(this.sessionFile)) {
        logger.warn('No session to export');
        return false;
      }

      const sessionData = fs.readFileSync(this.sessionFile, 'utf8');
      fs.writeFileSync(exportPath, sessionData);
      
      logger.info('Session exported successfully', { exportPath });
      return true;
    } catch (error) {
      logger.error('Failed to export session', error);
      return false;
    }
  }

  // Import session from backup
  importSession(importPath) {
    try {
      if (!fs.existsSync(importPath)) {
        logger.error('Import file does not exist', { importPath });
        return false;
      }

      this.ensureSessionDirectory();
      const sessionData = fs.readFileSync(importPath, 'utf8');
      
      // Validate JSON
      JSON.parse(sessionData);
      
      fs.writeFileSync(this.sessionFile, sessionData);
      
      logger.info('Session imported successfully', { importPath });
      return true;
    } catch (error) {
      logger.error('Failed to import session', error);
      return false;
    }
  }
}

module.exports = SessionManager;
