{"level":"error","message":"Failed to navigate to trading page","stack":"TimeoutError: Waiting for selector `input[type=\"text\"]` failed: Waiting failed: 10000ms exceeded\n    at new WaitTask (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\WaitTask.js:50:34)\n    at IsolatedWorld.waitForFunction (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Realm.js:25:26)\n    at PQueryHandler.waitFor (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\QueryHandler.js:170:95)\n    at runNextTicks (node:internal/process/task_queues:60:5)\n    at listOnTimeout (node:internal/timers:545:9)\n    at process.processTimers (node:internal/timers:519:7)\n    at async CdpFrame.waitForSelector (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Frame.js:494:21)\n    at async CdpPage.waitForSelector (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Page.js:1351:20)\n    at async DOMController.navigateToTradingPage (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:62:7)\n    at async PocketOptionBot.initialize (E:\\Code\\pocket-bot-v2\\src\\bot.js:32:26)","timestamp":"2025-05-28 18:02:17"}
{"level":"error","message":"Failed to navigate to trading page","stack":"TimeoutError: Waiting for selector `input[type=\"text\"]` failed: Waiting failed: 10000ms exceeded\n    at new WaitTask (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\WaitTask.js:50:34)\n    at IsolatedWorld.waitForFunction (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Realm.js:25:26)\n    at PQueryHandler.waitFor (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\QueryHandler.js:170:95)\n    at runNextTicks (node:internal/process/task_queues:60:5)\n    at listOnTimeout (node:internal/timers:545:9)\n    at process.processTimers (node:internal/timers:519:7)\n    at async CdpFrame.waitForSelector (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Frame.js:494:21)\n    at async CdpPage.waitForSelector (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Page.js:1351:20)\n    at async DOMController.navigateToTradingPage (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:91:4)\n    at async DOMController.handleLogin (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:522:5)","timestamp":"2025-05-28 18:09:39"}
{"level":"error","message":"Failed to select timeframe","stack":"Error: Node is either not clickable or not an Element\n    at CdpElementHandle.clickablePoint (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:673:23)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:250:32)\n    at async CdpElementHandle.click (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:703:30)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:253:36)\n    at async DOMController.selectTimeframe (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:166:5)\n    at async PocketOptionBot.setupTradingParameters (E:\\Code\\pocket-bot-v2\\src\\bot.js:73:4)\n    at async PocketOptionBot.initialize (E:\\Code\\pocket-bot-v2\\src\\bot.js:41:4)\n    at async main (E:\\Code\\pocket-bot-v2\\src\\bot.js:377:23)","timestamp":"2025-05-28 18:10:10"}
{"level":"error","message":"Failed to set trade amount","stack":"Error: Node is either not clickable or not an Element\n    at CdpElementHandle.clickablePoint (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:673:23)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:250:32)\n    at async CdpElementHandle.click (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:703:30)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:253:36)\n    at async DOMController.setTradeAmount (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:194:5)\n    at async PocketOptionBot.setupTradingParameters (E:\\Code\\pocket-bot-v2\\src\\bot.js:76:4)\n    at async PocketOptionBot.initialize (E:\\Code\\pocket-bot-v2\\src\\bot.js:41:4)\n    at async main (E:\\Code\\pocket-bot-v2\\src\\bot.js:377:23)","timestamp":"2025-05-28 18:10:12"}
{"level":"error","message":"WebSocket error","stack":"Error: Unexpected server response: 302\n    at ClientRequest.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\ws\\lib\\websocket.js:913:7)\n    at ClientRequest.emit (node:events:519:28)\n    at HTTPParser.parserOnIncomingClient (node:_http_client:702:27)\n    at HTTPParser.parserOnHeadersComplete (node:_http_common:118:17)\n    at TLSSocket.socketOnData (node:_http_client:544:22)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)\n    at TLSWrap.onStreamRead (node:internal/stream_base_commons:191:23)","timestamp":"2025-05-28 18:11:00"}
{"level":"error","message":"Failed to select timeframe","stack":"Error: Node is either not clickable or not an Element\n    at CdpElementHandle.clickablePoint (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:673:23)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:250:32)\n    at async CdpElementHandle.click (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:703:30)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:253:36)\n    at async DOMController.selectTimeframe (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:166:5)\n    at async PocketOptionBot.setupTradingParameters (E:\\Code\\pocket-bot-v2\\src\\bot.js:73:4)\n    at async PocketOptionBot.initialize (E:\\Code\\pocket-bot-v2\\src\\bot.js:41:4)\n    at async main (E:\\Code\\pocket-bot-v2\\src\\bot.js:377:23)","timestamp":"2025-05-28 19:13:36"}
{"level":"error","message":"Failed to set trade amount","stack":"Error: Node is either not clickable or not an Element\n    at CdpElementHandle.clickablePoint (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:673:23)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:250:32)\n    at async CdpElementHandle.click (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:703:30)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:253:36)\n    at async DOMController.setTradeAmount (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:194:5)\n    at async PocketOptionBot.setupTradingParameters (E:\\Code\\pocket-bot-v2\\src\\bot.js:76:4)\n    at async PocketOptionBot.initialize (E:\\Code\\pocket-bot-v2\\src\\bot.js:41:4)\n    at async main (E:\\Code\\pocket-bot-v2\\src\\bot.js:377:23)","timestamp":"2025-05-28 19:13:38"}
{"level":"error","message":"WebSocket error","stack":"Error: Unexpected server response: 302\n    at ClientRequest.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\ws\\lib\\websocket.js:913:7)\n    at ClientRequest.emit (node:events:519:28)\n    at HTTPParser.parserOnIncomingClient (node:_http_client:702:27)\n    at HTTPParser.parserOnHeadersComplete (node:_http_common:118:17)\n    at TLSSocket.socketOnData (node:_http_client:544:22)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)\n    at TLSWrap.onStreamRead (node:internal/stream_base_commons:191:23)","timestamp":"2025-05-28 19:14:27"}
