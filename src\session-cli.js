#!/usr/bin/env node

const SessionManager = require('./session-manager');
const logger = require('./utils/logger');

class SessionCLI {
  constructor() {
    this.sessionManager = new SessionManager();
  }

  showHelp() {
    console.log(`
Pocket Option Bot - Session Management CLI

Usage: node src/session-cli.js <command>

Commands:
  info      - Show current session information
  clear     - Clear saved session data
  export    - Export session to backup file
  import    - Import session from backup file
  help      - Show this help message

Examples:
  node src/session-cli.js info
  node src/session-cli.js clear
  node src/session-cli.js export ./session-backup.json
  node src/session-cli.js import ./session-backup.json
`);
  }

  showSessionInfo() {
    const info = this.sessionManager.getSessionInfo();
    
    if (!info) {
      console.log('❌ No saved session found');
      return;
    }

    console.log('📊 Session Information:');
    console.log(`   Status: ${info.isValid ? '✅ Valid' : '❌ Expired'}`);
    console.log(`   Age: ${info.ageMinutes} minutes (${info.ageHours} hours)`);
    console.log(`   Created: ${info.timestamp}`);
    console.log(`   URL: ${info.url}`);
    console.log(`   Cookies: ${info.cookieCount} stored`);
    
    if (!info.isValid) {
      console.log('⚠️  Session has expired and will be ignored');
    }
  }

  clearSession() {
    const success = this.sessionManager.clearSession();
    
    if (success) {
      console.log('✅ Session cleared successfully');
    } else {
      console.log('❌ Failed to clear session');
    }
  }

  exportSession(exportPath) {
    if (!exportPath) {
      console.log('❌ Please provide export path');
      console.log('Usage: node src/session-cli.js export <path>');
      return;
    }

    const success = this.sessionManager.exportSession(exportPath);
    
    if (success) {
      console.log(`✅ Session exported to: ${exportPath}`);
    } else {
      console.log('❌ Failed to export session');
    }
  }

  importSession(importPath) {
    if (!importPath) {
      console.log('❌ Please provide import path');
      console.log('Usage: node src/session-cli.js import <path>');
      return;
    }

    const success = this.sessionManager.importSession(importPath);
    
    if (success) {
      console.log(`✅ Session imported from: ${importPath}`);
    } else {
      console.log('❌ Failed to import session');
    }
  }

  run() {
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
      case 'info':
        this.showSessionInfo();
        break;
      
      case 'clear':
        this.clearSession();
        break;
      
      case 'export':
        this.exportSession(args[1]);
        break;
      
      case 'import':
        this.importSession(args[1]);
        break;
      
      case 'help':
      case '--help':
      case '-h':
        this.showHelp();
        break;
      
      default:
        console.log('❌ Unknown command:', command);
        this.showHelp();
        process.exit(1);
    }
  }
}

// Run CLI if this file is executed directly
if (require.main === module) {
  const cli = new SessionCLI();
  cli.run();
}

module.exports = SessionCLI;
