{"version": 3, "file": "css/chat.min.css", "mappings": "AAOA,UACE,uBAAwB,CACxB,4BAA6B,CAC7B,iCAAkC,CAClC,iCAAkC,CAKlC,aCfA,uCDcA,UAEA,mCACA,eACA,gBACA,qBAEA,YACE,uBAGF,8EAQE,eADA,gBADA,QAEA,CAGF,cAEE,YADA,cAEA,YACA,eAGF,YAEE,eAEA,gDAHA,oBAKE,CAIJ,0BAEE,SACA,UAGF,aACE,gBAGF,uQAWE,0BAGF,oBACE,gBAEA,uBADA,kBACA,CAIF,+DAIE,kBAGA,oDAJA,cAKA,eACA,iBAJA,iBACA,UAGA,CAGF,2BACE,6DACA,gBAEA,uBADA,kBACA,CAGF,4CAEE,YACA,eAGF,+BACE,UAIA,0BAGE,SAAQ,CAFR,kBACA,UACA,CAEA,8CACE,0NAIJ,yBACE,+CACA,4CACA,gEAEA,gCACE,WACA,qBACA,mBAQA,wBAEA,4BADA,wBAJA,kBAFA,mBACA,iBAJA,YACA,cAMA,YALA,iBAIA,UAIA,CAKN,mBACE,gBACA,YAGF,gBAIE,eAHA,qBAEA,eADA,kBAEA,CAQE,wGACE,UAEA,6EACE,SACA,iBAGF,qEAKE,gBAJA,YAEA,SACA,eAFA,OAGA,CAKN,uCAGE,YAEA,4DACE,iBAKF,2CAEE,SACA,iBAFA,iBAEA,CAMN,gBAKE,SAGA,YANA,OADA,kBAEA,QACA,MAGA,WA7MA,CA4MA,UAEA,CAEA,8BACE,iCACA,yDAEA,8DAFA,sBACA,WACA,CAGF,kCACE,YAEA,gDACE,UACA,UAIJ,wBA1BF,gBA2BI,WAjOF,EAsOF,qBAEE,eACA,gBAFA,cAEA,CAEA,iCAEE,mBADA,iBACA,CAEA,mDACE,iCACA,uDACA,kBAEA,8DAEE,YADA,UACA,CAIJ,mDACE,kBAGF,4CACE,gBAKJ,kCACE,eAEA,sDACE,gBAKJ,+CACE,iCACA,+CAEA,yDAEE,mBADA,eACA,CAIA,8FACE,iCACA,+EACA,4CACA,wBACA,mBAEA,aAHA,uBAEA,WACA,CAEA,mGAEE,eADA,eACA,CAGF,yGAEE,YADA,UACA,CAKJ,6FACE,YAEA,0GACE,mBAGF,6GACE,oBACA,WAEA,mHAEE,6BADA,WACA,CAIJ,2GACE,WAIJ,gFAEE,aADA,WACA,CAGF,8FAEE,cADA,iBACA,CAEA,oHAEE,YACA,eAFA,iBAEA,CAEA,yHACE,OACA,UAEA,oIAEE,YADA,UACA,CAIJ,0HACE,QACA,UAEA,qIAEE,YADA,UACA,CAOV,6EACE,iBAEA,+EAEE,eADA,eACA,CAIJ,6DACE,+EAEA,2FAEE,aAAY,CADZ,eACA,CAGF,oFACE,WAOR,mBAEE,wBAEA,4BADA,sBAFA,kBAGA,CAIF,oBAGE,yBACA,kBAGA,oDAMA,eAZA,qBAQA,eADA,gBAEA,gBALA,aAMA,kBACA,4FAVA,mBAIA,UAOA,CAEA,gCACE,oBAGF,0BACE,YAIJ,yBAGE,YACA,eAHA,aACA,UAEA,CAEA,oCACE,eAKJ,qBACE,qBAEA,yBACE,cAEA,YACA,oBAFA,UAEA,CAGF,oCACE,oBAKJ,qBACE,iCACA,+EACA,YAGE,0DACE,iCACA,4CACA,+CACA,oBAEA,4EACE,kBACA,0BACA,eAEA,uFAKE,YAJA,kBACA,WACA,SACA,UACA,CAIJ,4EACE,iCACA,4CACA,+CACA,iBAEA,uFAGE,YAFA,iBACA,UACA,CAMR,qCACE,kBAKJ,uBACE,iCACA,+EACA,YAEA,2CACE,aAEA,8DACE,kBAEA,yEAME,eADA,YAJA,kBACA,WACA,SACA,UAEA,CAOR,uBACE,iCACA,+EACA,YAEA,2CACE,iCACA,4CACA,+CACA,aAEA,8CACE,0BAGA,eADA,kBADA,kBAEA,CAIA,gEAEE,YADA,UACA,CAKN,yCACE,oBAKJ,yBACE,iCAEA,4BACE,0BAEA,kBADA,kBACA,CAEA,8BAEE,iCADA,kBAEA,4CACA,wBAEA,eAFA,uBACA,gBACA,CAEA,yCAGE,YAFA,iBACA,UACA,CAGF,2CAUE,oCANA,yBACA,kBAIA,WAFA,eACA,iBAFA,cALA,kBACA,UACA,OAOA,CAEA,iDACE,aAKN,uCACE,eAMN,oBAIE,SADA,gBAGA,oBALA,kBACA,MAGA,iBACA,CAEA,iCAEE,YAEA,mBAHA,kBAEA,kBACA,CAKJ,2BAIE,iBADA,UAFA,kBACA,UAEA,CAEA,uCAEE,gBADA,gBACA,CAEA,0CACE,eACA,kBAIJ,0CAKE,yBAHA,OAIA,UALA,kBAEA,QACA,SAIA,gEACA,gEAFA,iBAEA,CAEA,6CACE,iBAGF,6CACE,kBAGF,4CACE,cACA,iBACA,kBAIJ,0EACE,UAEA,wDADA,kBACA,CAGF,yDAKE,YAJA,kBACA,WACA,MACA,UACA,CAEA,oEAGE,YAFA,eAGA,0DAFA,UAEA,CAMN,qBAEE,wBACA,qBAFA,iBAEA,CAEA,sCACE,iCACA,4CACA,uDACA,aAEA,wDACE,kBAGF,uDACE,kBAEA,gEAEE,+BADA,6BACA,CAIA,0FAUE,oCANA,yBACA,kBAFA,YAMA,WAFA,cALA,SAMA,iBAFA,cALA,kBAUA,8DAEA,gGACE,aAGF,qGAEE,WADA,UAEA,aAGF,kGAIE,YAHA,UACA,QACA,MAEA,sCAGF,0GAEE,qCADA,wBACA,CAMR,yDACE,+BAIA,eACA,gBAHA,iBADA,gBAEA,SAEA,CAEA,4DACE,iCACA,4CACA,eAGF,2DACE,eACA,iBAGF,+DACE,cAGF,iEACE,gBAEA,uBADA,kBACA,CAGF,yEAKE,YAFA,iBAFA,kBACA,QAEA,UACA,CAGF,oEAGE,YAFA,gBACA,UACA,CAGF,iEACE,gBAEA,uBADA,kBACA,CAGF,gEACE,qBAIA,YAHA,mBACA,UACA,UACA,CAIJ,sDAGE,kBADA,aAEA,gBAHA,iBAGA,CAEA,6EAKE,8BADA,4BAGA,cADA,iBALA,kBACA,WACA,QAIA,CAIA,2EAEE,eADA,kBAEA,qBAMR,uCAKE,YAJA,kBACA,WACA,QACA,UACA,CAGF,gCAKE,QAAO,CAHP,OADA,kBAEA,QACA,KACA,CAGF,gCACE,mBAKJ,qBACE,iCACA,+EACA,YAIE,wEACE,iCACA,4CACA,yBAEA,eAFA,8BACA,gBACA,CAEA,iFACE,iCACA,+BAGA,eAFA,gBACA,cACA,CAEA,sFACE,gBAEA,uBADA,kBACA,CAIJ,mFACE,cAEA,YACA,WAFA,UAEA,CAEA,yFAEE,YADA,UACA,CAEA,+FACE,UAMR,iEAGE,iCAFA,kBACA,WAEA,4CACA,yBAEA,YAFA,8BACA,YACA,CAEA,gGACE,iBAGF,gFAEE,OAGA,UAJA,kBAEA,QACA,SAGA,gEACA,gEAFA,iBAEA,CAEA,mFACE,UAGF,mFAEE,0BACA,wBAFA,iBAEA,CAEA,8FACE,mBAIJ,kFACE,cACA,kBAEA,6FAKE,YAFA,iBADA,kBADA,sBAGA,UACA,CAMR,uEAEE,cADA,iBACA,CAEA,gFAEE,oCADA,kCACA,CAIJ,sEACE,0BAEA,yEACE,iCACA,4CACA,eAEA,8EACE,gBACA,uBAGF,oFAGE,YAFA,gBAGA,eAFA,UAEA,CAIJ,wEACE,iCACA,4CAEA,mFAGE,YAFA,sBACA,UACA,CAGF,6EACE,aAKN,+FACE,UAEA,wDADA,kBACA,CAKJ,wCAEE,YADA,iBACA,CAEA,kDAKE,QAAO,CAHP,OADA,kBAEA,QACA,KACA,CAKJ,uCACE,kBACA,WAEA,0DAGE,iBAFA,kBACA,UACA,CAGE,6EAME,eAJA,qBAGA,YAJA,kBAEA,mBACA,UAEA,CAEA,wFAKE,YAHA,SADA,kBAEA,QAGA,sEAFA,UAEA,CAGF,yFACE,YAIJ,0FAGE,WADA,UADA,iBAEA,CAGF,mFAEE,eADA,kBACA,CAGF,oFAEE,mBADA,mBAEA,YACA,kBAEA,yFACE,qBACA,aAEA,qGAGE,YAFA,sBACA,UACA,CAGF,+FACE,eAKN,wFACE,qBAEA,iGAGE,YAEA,WAJI,CAOJ,iBADA,iBADA,eALI,CAGJ,iBAIA,CAEA,2dAIE,uBAIJ,2GAGE,6BADA,eADA,eAEA,CAEA,kHACE,gBAIJ,mHAOE,eADA,YAJA,UAEA,gBAHA,kBAEA,QAEA,UAEA,CAGF,8HAEE,YADA,UACA,CAGF,iHACE,cAIJ,yFAGE,WAFA,kBACA,UACA,CAKN,2DAKE,kBADA,yBAIA,oFANA,UAOA,UAHA,aALA,kBAEA,QAQA,8DACA,gEAFA,kBAHA,WAKA,CAEA,qFAEE,eADA,kBACA,CAGF,qFACE,iCACA,eACA,4CAEA,kBADA,gBACA,CAKA,yFACE,qBAEA,YADA,UACA,CAGF,uFAGE,iBADA,eACA,CASJ,kEACE,UAEA,wDADA,kBACA,CAKN,0CAGE,YAFA,kBACA,UACA,CAEA,qDAEE,YADA,UACA,CAMN,wBACE,iCACA,yBAEA,uBAFA,8BACA,aAEA,oEAEA,4CAGE,YAFA,kBACA,UACA,CAEA,gDAGE,YAFA,kBACA,UACA,CAGF,+DAIE,mBAEA,WAJA,UADA,kBAEA,SAEA,SACA,CAEA,mEAGE,WAFA,WACA,SACA,CAIJ,mEACE,kBAEA,4EAEE,yCADA,uCACA,CAKN,8CACE,+BAEA,iBADA,eACA,CAEA,mEACE,iCACA,4CACA,yBAEA,eAFA,8BAGA,gBAFA,iBAEA,CAEA,8EACE,iCACA,4CACA,gBAEA,yFAEE,YADA,UACA,CAGF,wGAGE,YAFA,iBACA,UACA,CAGF,8FACE,iBAGF,yGAGE,YAFA,iBACA,UACA,CAEA,mHAEE,YADA,UACA,CAIJ,gHAEE,YADA,WAEA,aAKE,sGACE,cAQZ,0CACE,+CAEA,4CACE,mBAGA,eADA,eADA,gBAEA,CAWA,2ZACE,6BACA,mBAEA,2QAEE,YADA,UACA,CAON,2DACE,qBAKF,yDACE,qBAKF,uDACE,qBAOA,0HACE,cAGF,oIACE,gzFAOR,6BAEE,kBAEA,eACA,mBAFA,aAFA,iBAIA,CAEA,iCACE,0BAGF,gDACE,eAGF,gEAEE,sBAEA,sEACE,uBACA,kBAIJ,wCAEE,mBAEA,uBAIA,eACA,gBARA,eAKA,mBACA,iBAFA,eAIA,CAEA,2CAEE,eADA,eACA,CAGF,4CACE,iBAGF,0CAEE,iBADA,cACA,CAIJ,iCACE,cAKF,0CACE,kBAGF,oCACE,qBAIA,YAHA,mBACA,UACA,UACA,CAGF,sDAEE,kBADA,cAEA,iBAEA,0DACE,iCACA,4CACA,cAGF,wDACE,0BAGF,6DAEE,eAAc,CADd,gBACA,CAIJ,kDACE,iCACA,4CACA,qBAEA,eAFA,yBACA,cACA,CAEA,iEACE,gBAEA,4EAEE,YADA,UACA,CAIJ,iEACE,eACA,iBAMN,wBAGE,6BACA,kBAEA,eAJA,gBAGA,aAJA,iBAKA,CAEA,0CACE,iCACA,4CACA,uDAEA,mDAEE,+BADA,6BACA,CAGF,gEACE,+BAEA,iBADA,eACA,CASA,uLACE,iCACA,4CACA,yBACA,eADA,6BACA,CAGF,wEACE,iCACA,4CACA,uDAEA,8EACE,eAGF,8EACE,eAMR,8CACE,iCACA,4CACA,uDACA,gBAEA,wDAGE,eAFA,SACA,WACA,CAGF,kEAEE,iBADA,sBACA,CAKF,sDACE,iCACA,4CACA,uDAEA,4DACE,eAGF,4DACE,eAGF,gEAEE,eADA,gBACA,CAQN,gCACE,gBAGF,mDACE,iCACA,4CACA,uDAGF,+CACE,aAGE,8DACE,eAGF,8DACE,eAIJ,qDACE,gBAEA,4DACE,eAGF,4DACE,eAOR,sBAKE,SAEA,iCALA,OADA,kBAEA,QACA,MAEA,YAEA,4CACA,wBAEA,YAFA,uBACA,gBAEA,gBACA,8CAEA,wCACE,mBAGA,oFACA,oDACA,eAHA,cADA,WAIA,CAEA,0DACE,iCACA,4CACA,yBACA,0BACA,wBAEA,eAJA,8BAGA,aAEA,gBAOF,4DACE,aAGF,4DAGE,eADA,YACA,CAEA,uEACE,UAGF,6EAIE,WADA,gBAFA,gBACA,UAEA,CAEA,iFAEE,YADA,UACA,CAGF,mFACE,WACA,UAKN,2DACE,uBACA,qBACA,aAGF,4DACE,iBAGF,4DACE,gBAOJ,6CACE,iCACA,+EACA,WAMF,6CAIE,uCADA,uCACA,CAEA,2DAGE,iBARK,CAML,mBACA,iBAEA,oFACA,oBAEA,kFAEE,YACA,iBAFA,UAEA,CAIJ,gDAEE,iBArBK,CAsBL,oFAFA,eAEA,CAGF,gDACE,uBACA,qBAEA,4DACE,gBAGF,2DAEE,8BADA,8BAlCG,CAwCH,uIAEE,4BADA,4BACA,CAKN,qEACE,gBACA,uBAEA,uEACE,kBAEA,kBADA,kBACA,CAIJ,yEACE,cACA,kBAEA,oFAIE,YAFA,kBADA,sBAEA,UACA,CAIJ,+DACE,iCACA,uDACA,kBACA,aAGF,iEACE,mBAKA,yBAHA,iBAhFK,CA8EL,OACA,iBAGA,kBADA,UAEA,CAGF,0EAEE,YADA,UACA,CAQF,gDAEE,eADA,gBAEA,kBAGF,+DACE,gBAEA,iFACE,iBAGF,kEACE,UAGF,kEACE,wBAEA,6EACE,mBAIJ,iEACE,cACA,kBAEA,4EAKE,YAFA,iBADA,kBADA,sBAGA,UACA,CAKN,oEAEE,sBADA,iBACA,CAEA,wFAIE,aAFA,cADA,kBAEA,WACA,CAEA,4FAGE,aAFA,kBACA,WACA,CAGF,2GAIE,mBAEA,YAJA,UADA,kBAEA,UAEA,UACA,CAEA,+GAGE,YAFA,WACA,UACA,CAIJ,+GACE,kBAEA,wHAEE,aADA,WACA,CAYF,mqBACE,6BACA,mBAEA,+YAEE,YADA,UACA,CAON,uGACE,qBAKF,qGACE,qBAKF,mGACE,qBAOA,kNACE,cAGF,4NACE,gzFAYR,6DAKE,YAJA,kBACA,WACA,UACA,UACA,CAGF,qEACE,iCACA,4CACA,+CACA,cAGF,6EACE,iCACA,4CAEA,kBAEA,eACA,iBAJA,iBAEA,gBAEA,CAEA,wFAEE,YADA,UACA,CAIJ,4DACE,iCACA,+CAEA,2EACE,cAIJ,2EACE,iBAGF,8DACE,iCACA,4CACA,+CAEA,mBADA,eACA,CAEA,gEACE,iBAEA,uEACE,eAIJ,yFACE,iBACA,kBAEA,eADA,gBACA,CAEA,kGAEE,oCADA,oBACA,CAGF,gGAEE,qCADA,oBACA,CAGF,kGAEE,oCADA,oBACA,CAGF,qGAEE,qCADA,oBACA,CAGF,gGAEE,qCADA,oBACA,CAGF,6FAEE,qCADA,oBACA,CAGF,8FAEE,qCADA,oBACA,CAIJ,qFACE,iBACA,kBAEA,eADA,gBACA,CAEA,oGAEE,oCADA,oBACA,CAGF,gGAEE,qCADA,oBACA,CAGF,kGAEE,qCADA,oBACA,CAGF,oMAGE,qCADA,oBACA,CASR,uCACE,iCACA,4CACA,uDACA,mBAEA,wDAEE,kBADA,iBACA,CAEA,iEACE,mBAEA,yCADA,uCACA,CAIJ,uDACE,0BAEA,0DACE,eAEA,qEAEE,YADA,UACA,CAIJ,yDACE,iCACA,4CAEA,yEAIE,YAFA,iBADA,sBAEA,UACA,CAMR,sCACE,gBAKJ,cACE,cAII,iFAEE,aAAY,CADZ,iBACA,CAOF,8DACE,iCACA,4CACA,+CACA,oBAEA,gFAEE,aAAY,CADZ,iBACA,CAIA,2FACE,UACA,WAUJ,6EACE,UACA,WAON,gCAEE,iBADA,cACA,CAEA,2CACE,cAKN,yBACE,qBAII,iFAME,2BAA0B,CAD1B,+BAFA,2BACA,0BAHA,UACA,UAIA,CAKN,4CAGE,cADA,cADA,iBAEA,CAEA,uDAEE,aAAY,CADZ,gBACA,CAIJ,2CACE,UACA,WAMA,0DACE,cAGE,uEAEE,iBADA,iBACA,CAMJ,0EACE,0DAIJ,0EAEE,aAAY,CADZ,iBACA,CAKE,gFAEE,aAAY,CADZ,gBACA,CAQJ,gFAEE,aAAY,CADZ,iBACA,CAQA,4FACE,qBAGF,8FACE,UACA,WAGF,6FACE,UACA,WAKN,sEACE,UACA,WAMJ,4CAEE,oBADA,uBAGA,cAAa,CADb,iBACA,CAIA,qEAEE,aAAY,CADZ,gBACA,CAcF,mIANA,aAAY,CADZ,iBASE,CAGF,qEAEE,gBADA,cACA,CAGF,+EAEE,aAAY,CADZ,iBACA,CAGF,mEACE,iBAMR,uBACE,cAIJ,cAIE,eAFA,WACA,aAFA,UAGA,CAGF,wBACE,gBAGE,SAFA,UACA,MAEA,aAMF,6DAGE,SAFA,eACA,SAEA,8DACA,kJAGF,mFACE,8DE1zEA,uGAKE,yBAFA,yBACA,aACA,CAGF,iCACE,cAKE,+DAEE,yBADA,oBACA,CAKF,iDAEE,yBADA,oBACA,CAMJ,0DAEE,mBADA,wBACA,CAGE,8FACE,WAKF,2FACE,aAKN,wDAGE,mBAFA,YACA,UACA,CAEA,wFAEE,mBADA,UACA,CAKN,uDACE,cACA,UAGF,8CACE,cACA,UAGF,kDACE,cACA,UAGF,6CACE,cACA,UAGF,6BACE,cAEA,sEAEE,cAMF,+CACE,yBAIJ,kCACE,qBAGF,0CACE,yBAIF,sCACE,cAGE,mEACE,aAEA,yEACE,aAOJ,uFACE,mBACA,YAKE,uHACE,mBAQV,qCAGE,yBADA,mBAEA,WAEA,sFAEE,yBAGF,qDACE,mCACA,yBACA,WAEA,2DACE,yBAIJ,qDACE,qCACA,yBACA,WAEA,2DACE,yBAIJ,mDACE,yBACA,yBACA,WAGF,oDAGE,yBAFA,yBACA,UACA,CAEA,+DACE,UAGF,0DACE,qBAGF,6DAEE,yBADA,oBACA,CAMJ,qDACE,aAOF,mMACE,yBAGF,sTAEE,yBAKJ,sCACE,aAKA,wDACE,yBAKM,yGACE,UAMJ,wGACE,UACA,YAGF,kGACE,YAIA,4NACE,UAYV,4DACE,yBAEA,+DACE,WAMN,4CACE,yBAEA,2DACE,WAGF,6DACE,cAEA,mEACE,WAMN,sCAEE,4BADA,aACA,CAEA,4CACE,yBAKE,2FACE,cAMJ,uEAEE,8BADA,wBACA,CAEA,8FACE,mBACA,WAUJ,yKACE,WAQJ,+DACE,mBACA,cAEA,wEACE,WAEA,0EACE,cAIJ,0EACE,aAIJ,wDACE,yBAII,gGACE,cAMJ,8EACE,aAGF,oFACE,UAIJ,uEACE,yBAEA,0EACE,4BAEA,gFACE,yBAOF,6JACE,aAQN,gEACE,WAGF,+DACE,cAIJ,6EACE,aAIA,mEACE,mCAKF,2EACE,yBAEA,+FACE,WAGF,gGACE,yBAKA,qNACE,aAIA,iOACE,aAMR,4EACE,yBAGF,kFACE,cAKF,sEACE,aAGF,4EACE,aASF,6DAEE,yBADA,UA7ckB,CAydlB,oWACE,cAIJ,kJACE,wBA9dsB,CAgetB,sJACE,cAGF,wKACE,qBAEA,kLACE,cAMJ,0LACE,yBAKN,mDACE,yBAIA,+EACE,cAGF,sEACE,cAGF,yEACE,cAMN,8CAEE,yBADA,UA1gBsB,CA6gBtB,qEACE,wBA9gBoB,CAihBtB,gDACE,WAGF,sDACE,cAGF,yDACE,qBAEA,8DACE,cAIJ,uEACE,yBAME,6FACE,UAGF,0FACE,aAGF,oGACE,aASJ,yFACE,WAGF,4FACE,cAIJ,kGACE,8BAGF,kDACE,mBACA,yBAKE,6EACE,WAGF,6EACE,cASJ,8EACE,UA3lBU,CA8lBZ,2EACE,aA9lBS,CAkmBT,2KACE,2BAKN,gEACE,mBAEA,uEACE,aA5mBS,CAgnBT,+EACE,cAKF,6EACE,UAxnBQ,CA+nBhB,uCACE,mCAEA,yDAEE,yBADA,UACA,CAEA,2EACE,wBAGF,2EAEE,4BADA,UACA,CAEA,wFACE,cAIJ,6EACE,WAGF,4EACE,yBAQF,6IAEE,yBAGF,4EACE,qBAGF,iEACE,yBAEA,mEACE,cAEA,8EACE,aAIJ,2HACE,yBAEA,6HACE,WAEA,wIACE,UAON,wFACE,yBAQN,kDACE,yBAGF,8DACE,oFACA,WAMA,uJACE,WAGF,8FACE,yBAEA,yGACE,aAKF,mFACE,4BAEA,yFACE,yBAIJ,kFACE,cAKF,qGACE,yBASJ,0DACE,cAEA,0EACE,aAMJ,wEACE,cAGF,uEACE,WAOV,cAIE,mBAHA,yBAEA,UACA,CClxBE,2FAIE,sBACA,yBAFA,UAEA,CAGF,6BACE,cAKE,2DAEE,yBADA,oBACA,CAKF,6CAEE,sBADA,oBACA,CAMJ,sDAEE,gBADA,wBACA,CAGE,0FACE,cAKF,uFACE,aAKN,oDACE,gBACA,YACA,cAEA,oFAEE,mBADA,aACA,CAKN,mDACE,cACA,UAGF,0CACE,cACA,UAGF,8CACE,cACA,UAGF,yCACE,cACA,UAGF,yBACE,cAEA,8DAEE,cAMF,2CACE,yBAKJ,kCACE,cAGE,+DACE,aAEA,qEACE,aAOJ,mFAEE,sBADA,wBACA,CAEA,wFACE,cAGF,8FACE,aAMA,mHACE,mBAKN,sEACE,yBACA,wEAEA,4EACE,yBAOR,iCAEE,yBADA,UACA,CAEA,8EAEE,yBAGF,iDACE,yBACA,WAGF,iDACE,yBACA,WAGF,+CACE,yBACA,WAGF,gDAGE,yBAFA,yBACA,aACA,CAEA,2DACE,aAGF,sDACE,qBAGF,yDAGE,yBAFA,qBACA,UACA,CAMJ,iDACE,UAOF,2LACE,yBAGF,sSAEE,yBAKJ,kCACE,aAMA,oDACE,yBAGE,yFACE,YAEA,kGACE,UAWR,wDACE,yBAEA,2DACE,cAMN,wCACE,yBAMA,gHACE,cAEA,+DACE,cAMN,kCACE,4BACA,cAEA,wCACE,yBAKE,uFACE,cAMJ,mEAEE,gCADA,wBACA,CAEA,0FACE,mBACA,WAIA,wFACE,cAKN,yEACE,cAQJ,2DACE,mBAEA,4EADA,aACA,CAEA,oEACE,cAEA,sEACE,cAIJ,sEACE,aAIJ,oDACE,yBAII,4FACE,cAKN,0EACE,aAGF,mEACE,yBAEA,sEACE,4BAEA,4EACE,yBAIJ,qEACE,cAEA,gFACE,aAMR,yDACE,cAKE,iIACE,cAKN,yEACE,aAIA,+DACE,qCAMF,uEACE,yBAEA,2FACE,cAGF,4FACE,sBAIA,6MACE,aAIA,yNACE,aAMR,wEAEE,sBADA,iFACA,CAKF,kEACE,aAGF,wEACE,aAQF,yDAEE,sBADA,aACA,CAWA,oVACE,cAIJ,0IACE,sBAEA,8IACE,cAGF,gKACE,qBAEA,0KACE,cAON,4EACE,yBAIJ,+CACE,yBAIA,2EACE,cAGF,kEACE,cAGF,qEACE,cASJ,2GACE,sBAGF,4CACE,cAGF,kDACE,cAGF,qDACE,qBAEA,0DACE,cAIJ,mEACE,yBAEA,2EACE,cAOA,yFACE,aAGF,sFACE,aAGF,gGACE,aAQJ,qFACE,cAGF,wFACE,cAIJ,0FACE,mBAGF,8CACE,mBACA,yBAKE,yEACE,cAGF,yEACE,cAaJ,iJACE,aAtkBQ,CA0kBR,mKACE,yBAKN,4DACE,gBACA,yBAEA,mEACE,aAplBS,CAwlBT,2EACE,cAKF,yEACE,aAhmBM,CAumBd,mCACE,qCAEA,qDAEE,yBADA,aACA,CAEA,uEACE,wBAGF,uEAEE,4BADA,aACA,CAEA,oFACE,cAIJ,yEACE,cAGF,wEACE,yBAQF,qIAEE,yBAGF,wEACE,qBAGF,6DACE,yBAEA,+DACE,cAEA,0EACE,aAIJ,uHACE,sCAKF,oFAEE,yBADA,UACA,CAEA,+FACE,UAKN,8EAEE,yBADA,wBACA,CAQF,6DACE,cAGF,kFACE,cAGF,0FACE,yBAEA,qGACE,aAKF,+EACE,4BAEA,qFACE,yBAIJ,8EACE,cAKF,iGACE,yBASJ,sDACE,cAEA,sEACE,aAMJ,oEACE,cAGF,mEACE,cAOV,cAIE,gBAHA,yBACA,kBACA,aACA", "sources": ["webpack:///./sass/chat/_chat.scss", "webpack:///./sass/mixins/fonts/_NotoSans.scss", "webpack:///./sass/chat/_theme-dark-blue.scss", "webpack:///./sass/chat/_theme-light.scss"], "sourcesContent": ["@use \"sass:map\";\n@use \"@config/default\";\n@use \"@mixins/settings\" as *;\n@use \"@mixins/fonts/NotoSans\" as *;\n\n$w: #{settings(chat, width)};\n\n.chat_app {\n  --chat-avatar-size: 40px;\n  --chat-room-avatar-size: 40px; // Размер аватарки комнаты, выводится сверху внутри комнаты\n  --chat-room-user-avatar-size: 30px; // Размер аватарки юзеров внутри комнаты\n  --chat-room-info-avatar-size: 80px; // Размер аватарки комнаты, выводится сверху внутри комнаты\n\n  @include NotoSans;\n\n  z-index: settings(zIndex, chat);\n  display: none;\n  -webkit-font-smoothing: antialiased;\n  font-size: 14px;\n  line-height: 1.4;\n  word-wrap: break-word;\n\n  * {\n    outline: none !important;\n  }\n\n  h1,\n  h2,\n  h3,\n  h4,\n  h5,\n  h6 {\n    margin: 0;\n    font-weight: 400;\n    font-size: 14px;\n  }\n\n  img {\n    display: block;\n    border: none;\n    height: auto;\n    max-width: 100%;\n  }\n\n  a {\n    text-decoration: none;\n    cursor: pointer;\n\n    &:hover,\n    &:focus {\n      text-decoration: none;\n    }\n  }\n\n  ul,\n  ol {\n    margin: 0;\n    padding: 0;\n  }\n\n  ul {\n    list-style: none;\n  }\n\n  h1:last-child,\n  h2:last-child,\n  h3:last-child,\n  h4:last-child,\n  h5:last-child,\n  h6:last-child,\n  p:last-child,\n  ol:last-child,\n  ul:last-child,\n  li:last-child,\n  hr:last-child {\n    margin-bottom: 0 !important;\n  }\n\n  .one_line {\n    overflow: hidden;\n    white-space: nowrap;\n    text-overflow: ellipsis;\n  }\n\n  /* Form elements */\n  input[type=\"text\"],\n  textarea,\n  select {\n    display: block;\n    border-radius: settings(common, borderRadius);\n    padding: 9px 10px;\n    width: 100%;\n    box-sizing: border-box;\n    font-size: 14px;\n    line-height: 20px;\n  }\n\n  input[type=\"text\"] {\n    appearance: none;\n    overflow: hidden;\n    white-space: nowrap;\n    text-overflow: ellipsis;\n  }\n\n  input[type=\"text\"],\n  select {\n    height: 40px;\n    min-width: 70px;\n  }\n\n  input[type=\"checkbox\"] {\n    padding: 0;\n  }\n\n  .checkbox {\n    & > input {\n      position: absolute;\n      z-index: -1;\n      opacity: 0;\n\n      &:checked + span::before {\n        background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e\");\n      }\n    }\n\n    & > span {\n      display: inline-flex;\n      align-items: center;\n      user-select: none;\n\n      &::before {\n        content: \"\";\n        display: inline-block;\n        flex-grow: 0;\n        flex-shrink: 0;\n        margin-right: 5px;\n        border-style: solid;\n        border-width: 1px;\n        border-radius: 5px;\n        width: 18px;\n        height: 18px;\n        background-position: center center;\n        background-size: 50% 50%;\n        background-repeat: no-repeat;\n      }\n    }\n  }\n\n  textarea {\n    min-height: 60px;\n    resize: none;\n  }\n\n  label {\n    display: inline-block;\n    vertical-align: top;\n    font-size: 14px;\n    cursor: pointer;\n  }\n\n  /* Select */\n  .v-select {\n    .vs__dropdown-toggle {\n      padding: 0;\n\n      .vs__selected-options {\n        padding: 0;\n\n        .vs__selected {\n          margin: 0;\n          padding: 9px 10px;\n        }\n\n        input {\n          border: none;\n          width: 0;\n          height: 0;\n          min-width: auto;\n          background: none;\n        }\n      }\n    }\n\n    .vs__dropdown-menu {\n      /* max-height: 150px; */\n\n      border: none;\n\n      .vs__dropdown-option {\n        padding: 5px 10px;\n      }\n    }\n\n    &.vs--open {\n      .vs__selected {\n        position: relative;\n        margin: 0;\n        padding: 9px 10px;\n      }\n    }\n  }\n\n  /* Wrapper */\n  .chat {\n    position: relative;\n    left: 0;\n    right: 0;\n    top: 0;\n    bottom: 0;\n    z-index: 80;\n    width: $w;\n    height: 100%;\n\n    .chat_wrapper {\n      display: flex;\n      flex-direction: column;\n      height: 100%;\n      backface-visibility: hidden;\n    }\n\n    .scroll_container {\n      height: 100%;\n\n      .os-scrollbar {\n        padding: 0;\n        width: 8px;\n      }\n    }\n\n    @media (min-width: 768px) {\n      width: $w;\n    }\n  }\n\n  /* Form  */\n  .chat_form {\n    padding: 0 20px;\n    font-size: 13px;\n    line-height: 1.4;\n\n    .chat_field {\n      position: relative;\n      margin-bottom: 20px;\n\n      .chat_field_label {\n        display: flex;\n        justify-content: space-between;\n        margin-bottom: 3px;\n\n        .chat_icon {\n          width: 16px;\n          height: 16px;\n        }\n      }\n\n      .chat_field_value {\n        position: relative;\n      }\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n\n    /* Button */\n    .chat_button {\n      padding: 20px 0;\n\n      .chat_btn + .chat_btn {\n        margin-top: 20px;\n      }\n    }\n\n    /* File upload */\n    .chat_file_upload_wrapper {\n      display: flex;\n      justify-content: center;\n\n      .chat_btn {\n        padding-top: 8px;\n        padding-bottom: 8px;\n      }\n\n      .chat_file_upload_image {\n        .chat_file_upload_area {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          border-radius: 10px;\n          width: 130px;\n          height: 130px;\n\n          > span {\n            margin-top: 10px;\n            font-size: 12px;\n          }\n\n          .chat_icon {\n            width: 40px;\n            height: 40px;\n          }\n        }\n\n        /* Photo crop */\n        .chat_photo_crop_area {\n          width: 130px;\n\n          .cr-boundary {\n            border-radius: 10px;\n          }\n\n          .cr-slider-wrap {\n            margin: 0 16px 0 6px;\n            width: auto;\n\n            input {\n              border: none;\n              background-color: transparent;\n            }\n          }\n\n          .is-m-version & {\n            width: auto;\n          }\n        }\n\n        .chat_bg {\n          width: 100px;\n          height: 100px;\n        }\n\n        .chat_crop_area_labels {\n          position: relative;\n          margin: 0 auto;\n\n          .chat_crop_area_label {\n            position: absolute;\n            bottom: 40px;\n            font-size: 12px;\n\n            &.left {\n              left: 0;\n              top: - 30px;\n\n              .chat_icon {\n                width: 14px;\n                height: 14px;\n              }\n            }\n\n            &.right {\n              right: 0;\n              top: - 33px;\n\n              .chat_icon {\n                width: 20px;\n                height: 20px;\n              }\n            }\n          }\n        }\n      }\n\n      .chat_file_upload_description {\n        margin-left: 15px;\n\n        p {\n          margin-top: 10px;\n          font-size: 12px;\n        }\n      }\n\n      .is-m-version & {\n        flex-direction: column;\n\n        .chat_file_upload_description {\n          margin-top: 15px;\n          margin-left: 0;\n        }\n\n        .chat_file_upload_area {\n          width: auto;\n        }\n      }\n    }\n  }\n\n  /* Avatar */\n  .chat_bg {\n    border-radius: 100%;\n    background-position: 50%;\n    background-size: cover;\n    background-repeat: no-repeat;\n  }\n\n  /* Buttons */\n  .chat_btn {\n    display: inline-block;\n    vertical-align: top;\n    border-color: transparent;\n    border-radius: 5px;\n    padding: 10px;\n    width: 100%;\n    box-sizing: border-box;\n    font-weight: 500;\n    font-size: 12px;\n    line-height: 1.5;\n    text-align: center;\n    transition: color 0.3s, background-color 0.3s;\n    cursor: pointer;\n\n    .s-disabled {\n      pointer-events: none;\n    }\n\n    &:hover {\n      opacity: 0.85;\n    }\n  }\n\n  .chat_btn_icon {\n    padding: 12px;\n    width: 40px;\n    height: 40px;\n    min-width: 40px;\n\n    .chat_icon {\n      display: inline;\n    }\n  }\n\n  /* Icons */\n  .chat_icon {\n    display: inline-block;\n\n    svg {\n      display: block;\n      width: 100%;\n      height: 100%;\n      pointer-events: none;\n    }\n\n    &.chat_icon_role {\n      fill: #fff !important;\n    }\n  }\n\n  /* Main */\n  .chat_main {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n\n    .chat_main_header {\n      .chat_main_actions {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 20px 20px 0;\n\n        .chat_main_search {\n          position: relative;\n          flex: 1;\n          padding: 0 10px;\n\n          .chat_icon {\n            position: absolute;\n            right: 20px;\n            top: 12px;\n            width: 18px;\n            height: 18px;\n          }\n        }\n\n        .mark_read_button {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-left: 10px;\n\n          .chat_icon {\n            margin-right: 5px;\n            width: 16px;\n            height: 16px;\n          }\n        }\n      }\n    }\n\n    .chat_main_navs {\n      padding: 15px 20px;\n    }\n  }\n\n  /* Search */\n  .chat_search {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n\n    .chat_search_header {\n      padding: 20px;\n\n      .chat_search_field {\n        position: relative;\n\n        .chat_icon {\n          position: absolute;\n          right: 10px;\n          top: 12px;\n          width: 16px;\n          height: 16px;\n          cursor: pointer;\n        }\n      }\n    }\n  }\n\n  /* Chat create */\n  .chat_create {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n\n    .chat_create_header {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n\n      h4 {\n        flex: 1;\n        padding-right: 15px;\n        padding-left: 15px;\n        font-size: 16px;\n      }\n\n      .chat_btn {\n        .chat_icon {\n          width: 16px;\n          height: 16px;\n        }\n      }\n    }\n\n    .chat_create_type {\n      padding-bottom: 20px;\n    }\n  }\n\n  /* Tabs */\n  .chat_tabs_nav {\n    display: flex;\n\n    li {\n      flex: 1;\n      vertical-align: top;\n      margin-right: 10px;\n\n      a {\n        position: relative;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 7px 10px;\n        font-size: 12px;\n\n        .chat_icon {\n          margin-right: 5px;\n          width: 18px;\n          height: 18px;\n        }\n\n        .chat_number {\n          position: absolute;\n          right: 3px;\n          top: 3px;\n          border: 1px solid #009af9;\n          border-radius: 5px;\n          padding: 0 2px;\n          font-size: 10px;\n          line-height: 13px;\n          color: #fff;\n          background-color: rgb(0 154 249 / 60%);\n\n          &:empty {\n            display: none;\n          }\n        }\n      }\n\n      &:last-child {\n        margin-right: 0;\n      }\n    }\n  }\n\n  /* Chat tab */\n  .chat_tab {\n    position: absolute;\n    top: 0;\n    overflow: hidden;\n    height: 0;\n    visibility: hidden;\n    pointer-events: none;\n\n    &.s-tab_active {\n      position: relative;\n      height: auto;\n      visibility: visible;\n      pointer-events: all;\n    }\n  }\n\n  /* Room list */\n  .chat_tab_header {\n    position: relative;\n    z-index: 20;\n    padding: 0;\n    line-height: 15px;\n\n    .chat_title {\n      padding: 9px 14px;\n      line-height: 1.4;\n\n      h4 {\n        font-size: 16px;\n        text-align: center;\n      }\n    }\n\n    .chat_dropdown {\n      position: absolute;\n      left: 0;\n      right: 0;\n      top: 100%;\n      background-color: inherit;\n      opacity: 0;\n      visibility: hidden;\n      transform: translateY(-10px);\n      transition: all 0.2s ease-out;\n\n      ul {\n        padding: 5px 10px;\n      }\n\n      li {\n        position: relative;\n      }\n\n      a {\n        display: block;\n        padding: 10px 5px;\n        text-align: center;\n      }\n    }\n\n    .s-chat_tab_header_dropdown_open.chat_dropdown {\n      opacity: 1;\n      visibility: visible;\n      transform: translateY(0);\n    }\n\n    .chat_tab_header_menu_trigger {\n      position: absolute;\n      right: 13px;\n      top: 0;\n      width: 30px;\n      height: 100%;\n\n      .chat_icon {\n        margin-top: 7px;\n        width: 20px;\n        height: 20px;\n        transform: rotate(-90deg);\n      }\n    }\n  }\n\n  /* Room list item */\n  .chat_list {\n    position: relative;\n    border-bottom: 1px solid;\n    padding: 0 10px 0 3px;\n\n    .chat_list_inner {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 15px;\n\n      .chat_list_select {\n        padding-right: 5px;\n      }\n\n      .chat_list_image {\n        position: relative;\n\n        .chat_bg {\n          width: var(--chat-avatar-size);\n          height: var(--chat-avatar-size);\n        }\n\n        .chat_list_image_info {\n          .chat_number {\n            position: absolute;\n            left: 50%;\n            bottom: -5px;\n            border: 1px solid #009af9;\n            border-radius: 5px;\n            padding: 0 2px;\n            font-size: 9px;\n            line-height: 10px;\n            color: #fff;\n            background-color: rgba(0 154 249 / 60%);\n            transform: translateX(-50%);\n\n            &:empty {\n              display: none;\n            }\n\n            .chat_icon {\n              width: 6px;\n              height: 8px;\n              fill: rgb(249 229 150 / 100%);\n            }\n\n            &.mention {\n              left: auto;\n              right: 0;\n              top: 0;\n              bottom: auto;\n              transform: none;\n            }\n\n            &.chat_number_vip {\n              border: 1px solid #f9e596;\n              background-color: rgb(192 171 94 / 80%);\n            }\n          }\n        }\n      }\n\n      .chat_list_content {\n        flex-grow: 1;\n        overflow: hidden;\n        margin-left: 10px;\n        padding: 0;\n        font-size: 12px;\n        line-height: 1.4;\n\n        > h4 {\n          display: flex;\n          align-items: center;\n          font-size: 14px;\n        }\n\n        > p {\n          font-size: 12px;\n          line-height: 18px;\n        }\n\n        .blue {\n          color: #3498db;\n        }\n\n        .string {\n          overflow: hidden;\n          white-space: nowrap;\n          text-overflow: ellipsis;\n        }\n\n        .chat_icon_user {\n          position: relative;\n          top: 2px;\n          margin-right: 5px;\n          width: 10px;\n          height: 10px;\n        }\n\n        .chat_icon {\n          margin-left: 5px;\n          width: 15px;\n          height: 15px;\n        }\n\n        .nowrap {\n          overflow: hidden;\n          white-space: nowrap;\n          text-overflow: ellipsis;\n        }\n\n        .smile {\n          display: inline-block;\n          margin-bottom: -4px;\n          padding: 0;\n          width: 22px;\n          height: 22px;\n        }\n      }\n\n      &.chat_list_promo {\n        position: relative;\n        margin: 3px 0;\n        border-radius: 7px;\n        min-height: 80px;\n\n        .chat_list_promo_label {\n          position: absolute;\n          right: -1px;\n          top: -1px;\n          border-top-right-radius: 7px;\n          border-bottom-left-radius: 3px;\n          padding: 2px 18px;\n          font-size: 9px;\n        }\n\n        .chat_list_content {\n          > p {\n            font-style: italic;\n            font-size: 10px;\n            word-break: break-all;\n          }\n        }\n      }\n    }\n\n    .chat_icon_pinned {\n      position: absolute;\n      right: 12px;\n      top: 8px;\n      width: 18px;\n      height: 18px;\n    }\n\n    .chat_link {\n      position: absolute;\n      left: 0;\n      right: 0;\n      top: 0;\n      bottom: 0;\n    }\n\n    &:last-child {\n      border-bottom: none;\n    }\n  }\n\n  /* Room  */\n  .chat_room {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n\n    /* Room header */\n    .chat_room_header_wrapper {\n      .chat_room_header_pinned {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        padding: 7px 10px;\n        cursor: pointer;\n\n        div.text {\n          display: flex;\n          flex-grow: 1;\n          overflow: hidden;\n          padding: 0 10px;\n          font-size: 12px;\n\n          span {\n            overflow: hidden;\n            white-space: nowrap;\n            text-overflow: ellipsis;\n          }\n        }\n\n        .chat_icon {\n          display: block;\n          width: 20px;\n          height: 20px;\n          opacity: 0.5;\n\n          &.close {\n            width: 20px;\n            height: 20px;\n\n            &:hover {\n              opacity: 1;\n            }\n          }\n        }\n      }\n\n      .chat_room_header {\n        position: relative;\n        z-index: 20;\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        padding: 20px;\n        height: 80px;\n\n        .chat_room_header_menu_trigger {\n          margin-left: 10px;\n        }\n\n        .chat_dropdown {\n          position: absolute;\n          left: 0;\n          right: 0;\n          top: 100%;\n          opacity: 0;\n          visibility: hidden;\n          transform: translateY(-10px);\n          transition: all 0.2s ease-out;\n\n          ul {\n            padding: 0;\n          }\n\n          li {\n            position: relative;\n            border-bottom-style: solid;\n            border-bottom-width: 1px;\n\n            &:last-child {\n              border-bottom: none;\n            }\n          }\n\n          a {\n            display: block;\n            padding: 11px 15px;\n\n            .chat_icon {\n              vertical-align: middle;\n              margin-right: 25px;\n              margin-left: 18px;\n              width: 18px;\n              height: 18px;\n            }\n          }\n        }\n      }\n\n      .chat_room_header_image {\n        position: relative;\n        margin: 0 10px;\n\n        .chat_bg {\n          width: var(--chat-room-avatar-size);\n          height: var(--chat-room-avatar-size);\n        }\n      }\n\n      .chat_room_header_text {\n        flex: 1;\n\n        h4 {\n          display: flex;\n          align-items: center;\n          font-size: 16px;\n\n          span {\n            overflow: hidden;\n            text-overflow: ellipsis;\n          }\n\n          .chat_icon {\n            margin-left: 5px;\n            width: 18px;\n            height: 18px;\n            min-width: 18px;\n          }\n        }\n\n        p {\n          display: flex;\n          align-items: center;\n\n          .chat_icon {\n            vertical-align: middle;\n            width: 12px;\n            height: 12px;\n          }\n\n          span {\n            margin: 0 5px;\n          }\n        }\n      }\n\n      .s-chat_room_header_dropdown_open.chat_dropdown {\n        opacity: 1;\n        visibility: visible;\n        transform: translateY(0);\n      }\n    }\n\n    /* Room content */\n    .chat_room_content {\n      position: relative;\n      height: 100%;\n\n      .disabled {\n        position: absolute;\n        left: 0;\n        right: 0;\n        top: 0;\n        bottom: 0;\n      }\n    }\n\n    /* Room footer */\n    .chat_room_footer {\n      position: relative;\n      z-index: 10;\n\n      .chat_message_form {\n        position: relative;\n        z-index: 10;\n        padding: 5px 10px;\n\n        form {\n          .chat_trigger {\n            position: relative;\n            display: inline-block;\n            vertical-align: top;\n            width: 35px;\n            height: 45px;\n            cursor: pointer;\n\n            .chat_icon {\n              position: absolute;\n              left: 50%;\n              top: 50%;\n              width: 22px;\n              height: 22px;\n              transform: translate(-50%, -50%);\n            }\n\n            &:first-child {\n              float: right;\n            }\n          }\n\n          .chat_message_form_options {\n            position: absolute;\n            left: 10px;\n            bottom: 5px;\n          }\n\n          .chat_message_title {\n            margin-bottom: 10px;\n            font-size: 13px;\n          }\n\n          .chat_message_rating {\n            margin-bottom: 10px;\n            border-radius: 10px;\n            padding: 4px;\n            text-align: center;\n\n            span {\n              display: inline-block;\n              padding: 13px;\n\n              i.chat_icon {\n                vertical-align: middle;\n                width: 30px;\n                height: 30px;\n              }\n\n              &:hover {\n                cursor: pointer;\n              }\n            }\n          }\n\n          .chat_message_form_field {\n            margin: 0 35px 0 70px;\n\n            textarea {\n              $h: 40px;\n\n              border: none;\n              padding: 12px 10px;\n              height: $h;\n              min-height: $h;\n              max-height: 160px;\n              line-height: 16px;\n\n              &:-webkit-autofill,\n              &:-webkit-autofill:hover,\n              &:-webkit-autofill:focus,\n              &:-webkit-autofill:active {\n                background: transparent;\n              }\n            }\n\n            .chat_message_text {\n              padding: 5px 0 0;\n              font-size: 11px;\n              background-color: transparent;\n\n              strong {\n                font-weight: 500;\n              }\n            }\n\n            .chat_message_text_trigger {\n              position: absolute;\n              left: 100%;\n              top: 5px;\n              margin-left: 5px;\n              width: 35px;\n              height: 24px;\n              cursor: pointer;\n            }\n\n            .chat_message_text_trigger .chat_icon {\n              width: 14px;\n              height: 14px;\n            }\n\n            &.chat_message_form_rating {\n              margin-left: 0;\n            }\n          }\n\n          .chat_message_form_button {\n            position: absolute;\n            right: 10px;\n            bottom: 5px;\n          }\n        }\n      }\n\n      .chat_room_emotions {\n        position: absolute;\n        left: 10px;\n        right: 0;\n        bottom: calc(100% + 10px);\n        border-radius: 4px;\n        padding: 15px;\n        width: 260px;\n        box-shadow: 0 5px 20px rgba(0 0 0 / 15%);\n        opacity: 0;\n        visibility: hidden;\n        transform: translateY(20px);\n        transition: all 0.2s ease-out;\n\n        .chat_room_emotions_label {\n          margin-bottom: 10px;\n          font-size: 12px;\n        }\n\n        .chat_room_emotions_inner {\n          display: flex;\n          flex-wrap: wrap;\n          align-items: center;\n          margin-top: -10px;\n          margin-left: -10px;\n\n          //font-size: 17px;\n          //letter-spacing: 0.05em;\n\n          img {\n            display: inline-block;\n            width: 20px;\n            height: 20px;\n          }\n\n          > a {\n            //flex: 1 1 calc(100% / 8);\n            margin-top: 10px;\n            margin-left: 10px;\n\n            //max-width: calc(100% / 8);\n            //text-align: center;\n            //width: 28px;\n            //height: 28px;\n          }\n        }\n\n        &.s-open {\n          opacity: 1;\n          visibility: visible;\n          transform: translateY(0);\n        }\n      }\n    }\n\n    .chat_mention_button {\n      position: absolute;\n      right: 25px;\n      bottom: 10px;\n\n      .chat_icon {\n        width: 22px;\n        height: 22px;\n      }\n    }\n  }\n\n  /* Chat message */\n  .chat_message {\n    display: flex;\n    justify-content: space-between;\n    padding: 10px;\n    background: transparent;\n    transition: background 1s ease;\n\n    .chat_avatar_circle {\n      position: relative;\n      width: 30px;\n      height: 30px;\n\n      > svg {\n        position: absolute;\n        width: 30px;\n        height: 30px;\n      }\n\n      .chat_avatar_crown {\n        position: absolute;\n        left: 11px;\n        top: -3px;\n        border-radius: 100%;\n        width: 8px;\n        height: 8px;\n\n        svg {\n          margin: 1px;\n          width: 6px;\n          height: 6px;\n        }\n      }\n\n      .chat_avatar_circle_bg {\n        position: absolute;\n\n        .chat_bg {\n          width: var(--chat-room-user-avatar-size);\n          height: var(--chat-room-user-avatar-size);\n        }\n      }\n    }\n\n    .chat_message_wrapper {\n      flex-grow: 1;\n      overflow: hidden;\n      margin-left: 10px;\n\n      .chat_message_header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        margin-bottom: 3px;\n        font-size: 12px;\n        line-height: 1.2;\n\n        .chat_name {\n          display: flex;\n          align-items: center;\n          font-weight: 500;\n\n          .chat_icon {\n            width: 14px;\n            height: 14px;\n          }\n\n          .chat_icon.chat_icon_role {\n            margin-right: 5px;\n            width: 12px;\n            height: 12px;\n          }\n\n          .chat_name_text {\n            margin-right: 5px;\n          }\n\n          .chat_icon.chat_level_icon {\n            margin-right: 3px;\n            width: 12px;\n            height: 12px;\n\n            &.level_vip {\n              width: 16px;\n              height: 16px;\n            }\n          }\n\n          .chat_icon.chat_verification_icon {\n            width: 12px;\n            height: 12px;\n            fill: #8fa5bf;\n          }\n\n          &.chat_name_green {\n            a {\n              &:hover {\n                color: #61dc8a;\n              }\n            }\n          }\n        }\n      }\n    }\n\n    &.chat_message_info {\n      justify-content: center;\n\n      p {\n        border-radius: 10px;\n        padding: 5px 20px;\n        font-size: 12px;\n        cursor: pointer;\n      }\n    }\n\n    &.level_beginner,\n    &.level_master,\n    &.level_guru {\n      .chat_avatar_circle {\n        border: 1px solid transparent;\n        border-radius: 100%;\n\n        .chat_avatar_circle_bg {\n          border: 1px solid transparent;\n          border-radius: 100%;\n\n          .chat_bg {\n            width: 26px;\n            height: 26px;\n          }\n        }\n      }\n    }\n\n    &.level_beginner {\n      .chat_avatar_circle {\n        border-color: #32ac41;\n      }\n    }\n\n    &.level_master {\n      .chat_avatar_circle {\n        border-color: #2666c4;\n      }\n    }\n\n    &.level_guru {\n      .chat_avatar_circle {\n        border-color: #e6a22d;\n      }\n    }\n\n    &.level_vip,\n    &.level_vip_elite {\n      .chat_avatar_circle {\n        svg {\n          display: block;\n        }\n\n        .chat_bg {\n          clip-path: polygon(42.223% 9.81%, 42.223% 9.81%, 43.719% 9.159%, 45.255% 8.652%, 46.82% 8.29%, 48.405% 8.072%, 50% 8%, 51.595% 8.072%, 53.18% 8.29%, 54.745% 8.652%, 56.281% 9.159%, 57.777% 9.81%, 67.417% 14.548%, 77.079% 19.242%, 77.079% 19.242%, 78.514% 20.022%, 79.862% 20.925%, 81.117% 21.94%, 82.273% 23.062%, 83.323% 24.282%, 84.261% 25.592%, 85.082% 26.985%, 85.779% 28.452%, 86.346% 29.987%, 86.777% 31.58%, 89.137% 42.181%, 91.543% 52.772%, 91.543% 52.772%, 91.837% 54.397%, 91.982% 56.029%, 91.982% 57.658%, 91.838% 59.274%, 91.553% 60.868%, 91.128% 62.429%, 90.567% 63.949%, 89.871% 65.416%, 89.042% 66.823%, 88.083% 68.158%, 81.385% 76.64%, 74.725% 85.153%, 74.725% 85.153%, 73.656% 86.399%, 72.489% 87.531%, 71.233% 88.546%, 69.898% 89.44%, 68.493% 90.207%, 67.025% 90.844%, 65.504% 91.346%, 63.939% 91.709%, 62.339% 91.928%, 60.712% 92%, 50% 91.976%, 39.288% 92%, 39.288% 92%, 37.661% 91.928%, 36.061% 91.709%, 34.496% 91.346%, 32.975% 90.844%, 31.507% 90.207%, 30.102% 89.44%, 28.767% 88.546%, 27.511% 87.531%, 26.344% 86.399%, 25.275% 85.153%, 18.615% 76.64%, 11.917% 68.158%, 11.917% 68.158%, 10.958% 66.823%, 10.13% 65.416%, 9.433% 63.949%, 8.872% 62.429%, 8.448% 60.868%, 8.162% 59.274%, 8.018% 57.658%, 8.018% 56.029%, 8.163% 54.397%, 8.456% 52.772%, 10.863% 42.181%, 13.224% 31.58%, 13.224% 31.58%, 13.654% 29.987%, 14.221% 28.452%, 14.918% 26.985%, 15.739% 25.592%, 16.677% 24.282%, 17.728% 23.062%, 18.883% 21.94%, 20.138% 20.925%, 21.486% 20.022%, 22.921% 19.242%, 32.583% 14.548%, 42.223% 9.81%);\n        }\n      }\n    }\n  }\n\n  /* Chat message text */\n  .chat_message_text {\n    position: relative;\n    border-radius: 5px;\n    padding: 10px;\n    font-size: 13px;\n    line-height: 1.3125;\n\n    p a {\n      text-decoration: underline;\n    }\n\n    + .chat_message_text {\n      margin-top: 3px;\n    }\n\n    ul,\n    ol {\n      margin: 7px 0 7px 16px;\n\n      > li {\n        list-style-type: circle;\n        margin-bottom: 3px;\n      }\n    }\n\n    blockquote {\n      margin: 0 0 2px;\n      border-style: solid;\n      border-width: 0;\n      border-left-width: 2px;\n      padding-top: 2px;\n      padding-bottom: 2px;\n      padding-left: 6px;\n      font-size: 12px;\n      line-height: 1.2;\n\n      h5 {\n        font-weight: 500;\n        font-size: 12px;\n      }\n\n      img {\n        padding-bottom: 0;\n      }\n\n      p {\n        margin: 3px 0 0;\n        line-height: 15px;\n      }\n    }\n\n    img {\n      padding: 3px 0;\n\n      //max-height: 300px;\n    }\n\n    .text_hidden {\n      font-style: italic;\n    }\n\n    .smile {\n      display: inline-block;\n      margin-bottom: -4px;\n      padding: 0;\n      width: 22px;\n      height: 22px;\n    }\n\n    .chat_message_top_trader {\n      margin: 10px 0;\n      border-radius: 4px;\n      padding: 5px 10px;\n\n      > div {\n        display: flex;\n        align-items: center;\n        padding: 5px 0;\n      }\n\n      a {\n        flex: 1;\n      }\n\n      .smile {\n        margin-right: 7px;\n        margin-bottom: 0;\n      }\n    }\n\n    .chat_message_rating {\n      display: flex;\n      align-items: center;\n      justify-content: flex-end;\n      margin-top: 3px;\n      font-size: 12px;\n\n      .rating_button {\n        margin-left: 7px;\n\n        .chat_icon {\n          width: 16px;\n          height: 16px;\n        }\n      }\n\n      .rating_number {\n        font-size: 12px;\n        line-height: 16px;\n      }\n    }\n  }\n\n  /* Members item */\n  .chat_members {\n    position: relative;\n    margin: 5px 10px;\n    border: 1px solid transparent;\n    border-radius: 5px;\n    padding: 10px;\n    cursor: pointer;\n\n    .chat_members_box {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n\n      .chat_bg {\n        width: var(--chat-avatar-size);\n        height: var(--chat-avatar-size);\n      }\n\n      .chat_members_content {\n        flex-grow: 1;\n        overflow: hidden;\n        margin-left: 10px;\n\n        .chat_members_content_top {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          font-size: 12px;\n        }\n\n        .chat_members_content_bottom {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          font-size: 12px;\n        }\n\n        &.editors {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n\n          .name {\n            font-size: 12px;\n          }\n\n          .role {\n            font-size: 10px;\n          }\n        }\n      }\n    }\n\n    .chat_members_actions {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      margin-top: 10px;\n\n      .chat_btn {\n        margin: 0;\n        padding: 3px;\n        font-size: 10px;\n      }\n\n      .chat_btn + .chat_btn {\n        margin-top: 0 !important;\n        margin-left: 10px;\n      }\n    }\n\n    &.editors {\n      .chat_members_content {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n\n        .name {\n          font-size: 12px;\n        }\n\n        .role {\n          font-size: 10px;\n        }\n\n        .chat_btn {\n          padding: 1px 10px;\n          font-size: 10px;\n        }\n      }\n    }\n  }\n\n  /* Chat promotion */\n  .chat_promotion {\n    p + div {\n      margin-top: 20px;\n    }\n\n    .promotion_select_option {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n    }\n\n    .chat_promotion_info {\n      padding: 20px;\n\n      .amount {\n        .title {\n          font-size: 14px;\n        }\n\n        .value {\n          font-size: 22px;\n        }\n      }\n\n      .info {\n        margin-top: 15px;\n\n        .title {\n          font-size: 12px;\n        }\n\n        .value {\n          font-size: 13px;\n        }\n      }\n    }\n  }\n\n  /* Chat popup */\n  .chat_popup {\n    position: absolute;\n    left: 0;\n    right: 0;\n    top: 0;\n    bottom: 0;\n    z-index: 100;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    overflow: hidden;\n    font-size: 0;\n    text-align: left;\n    transition: all 0.2s;\n\n    .chat_popup_inner {\n      border-radius: 10px;\n      width: 300px;\n      max-width: 90%;\n      box-shadow: 0 5px 20px rgba(0 0 0 / 15%);\n      box-sizing: border-box;\n      font-size: 14px;\n\n      .chat_popup_title {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        border-bottom-style: solid;\n        border-bottom-width: 1px;\n        padding: 20px;\n        font-size: 16px;\n        text-align: left;\n\n        .description {\n\n        }\n      }\n\n      .chat_popup_padding {\n        padding: 20px;\n      }\n\n      .chat_popup_content {\n        //height: 100%;\n        padding: 20px;\n        font-size: 13px;\n\n        .chat_form {\n          padding: 0;\n        }\n\n        a.h-btn--deposit {\n          margin-top: 15px;\n          width: 100%;\n          font-weight: 600;\n          color: #fff;\n\n          svg {\n            width: 20px;\n            height: 18px;\n          }\n\n          &:hover {\n            color: #fff;\n            opacity: 1;\n          }\n        }\n      }\n\n      .chat_popup_bottom {\n        border-top-style: solid;\n        border-top-width: 1px;\n        padding: 20px;\n      }\n\n      .chat_popup_buttons {\n        padding-top: 20px;\n      }\n\n      .chat_btn + .chat_btn {\n        margin-top: 15px;\n      }\n    }\n  }\n\n  /* Chat full popup */\n  .chat_full_popup {\n    .chat_popup_inner {\n      display: flex;\n      flex-direction: column;\n      height: 90%;\n    }\n  }\n\n  /* Chat menu popup */\n  .chat_menu_popup {\n    .chat_popup_inner {\n      $brds: 4px;\n\n      box-shadow: none;\n      background-color: transparent !important;\n\n      .chat_message {\n        border-style: solid;\n        border-width: 1px;\n        border-radius: $brds;\n        box-shadow: 0 5px 20px rgba(0 0 0 / 15%);\n        pointer-events: none;\n\n        .chat_message_text img {\n          width: auto;\n          height: auto;\n          max-height: 100px;\n        }\n      }\n\n      ul {\n        margin-top: 20px;\n        border-radius: $brds;\n        box-shadow: 0 5px 20px rgba(0 0 0 / 15%);\n      }\n\n      li {\n        border-top-style: solid;\n        border-top-width: 1px;\n\n        &:first-child {\n          border-top: none;\n        }\n\n        &:last-child {\n          border-bottom-right-radius: $brds;\n          border-bottom-left-radius: $brds;\n        }\n\n        &:not(:first-child),\n        &:not(:last-child) {\n          a {\n            padding-right: 25px !important;\n            padding-left: 25px !important;\n          }\n        }\n      }\n\n      .chat_menu_message_hide {\n        border-top: none;\n        padding: 10px 20px 20px;\n\n        a {\n          border-radius: 4px;\n          padding-right: 10px;\n          padding-left: 10px;\n        }\n      }\n\n      li:not(.chat_menu_rating) a {\n        display: block;\n        padding: 11px 20px;\n\n        .chat_icon {\n          vertical-align: middle;\n          margin-right: 10px;\n          width: 18px;\n          height: 18px;\n        }\n      }\n\n      .chat_menu_rating {\n        display: flex;\n        justify-content: space-between;\n        margin-left: -20px;\n        padding: 20px;\n      }\n\n      .chat_menu_rating > a {\n        flex: 1;\n        margin-left: 20px;\n        border-radius: $brds;\n        width: 100%;\n        text-align: center;\n        background-color: #293145;\n      }\n\n      .chat_menu_rating .chat_icon {\n        width: 32px;\n        height: 32px;\n      }\n    }\n  }\n\n  /* Chat user popup */\n  .chat_user_popup {\n    .chat_popup_inner {\n      h4 {\n        font-weight: 500;\n        font-size: 18px;\n        text-align: center;\n      }\n\n      .chat_popup_links {\n        text-align: left;\n\n        + .chat_popup_links {\n          padding-top: 20px;\n        }\n\n        ul {\n          padding: 0;\n        }\n\n        li {\n          border-bottom: 1px solid;\n\n          &:last-child {\n            border-bottom: none;\n          }\n        }\n\n        a {\n          display: block;\n          padding: 12px 15px;\n\n          .chat_icon {\n            vertical-align: middle;\n            margin-right: 25px;\n            margin-left: 18px;\n            width: 18px;\n            height: 18px;\n          }\n        }\n      }\n\n      .chat_user_popup_image {\n        position: relative;\n        margin: 30px auto 10px;\n\n        .chat_avatar_circle {\n          position: relative;\n          margin: 0 auto;\n          width: 100px;\n          height: 100px;\n\n          > svg {\n            position: absolute;\n            width: 100px;\n            height: 100px;\n          }\n\n          .chat_avatar_crown {\n            position: absolute;\n            left: 38px;\n            top: -11px;\n            border-radius: 100%;\n            width: 24px;\n            height: 24px;\n\n            svg {\n              margin: 2px;\n              width: 20px;\n              height: 20px;\n            }\n          }\n\n          .chat_avatar_circle_bg {\n            position: absolute;\n\n            .chat_bg {\n              width: 100px;\n              height: 100px;\n            }\n          }\n        }\n\n        &.level_beginner,\n        &.level_master,\n        &.level_guru {\n          .chat_avatar_circle {\n            border: 2px solid transparent;\n            border-radius: 100%;\n\n            .chat_avatar_circle_bg {\n              border: 2px solid transparent;\n              border-radius: 100%;\n\n              .chat_bg {\n                width: 92px;\n                height: 92px;\n              }\n            }\n          }\n        }\n\n        &.level_beginner {\n          .chat_avatar_circle {\n            border-color: #32ac41;\n          }\n        }\n\n        &.level_master {\n          .chat_avatar_circle {\n            border-color: #2666c4;\n          }\n        }\n\n        &.level_guru {\n          .chat_avatar_circle {\n            border-color: #e6a22d;\n          }\n        }\n\n        &.level_vip,\n        &.level_vip_elite {\n          .chat_avatar_circle {\n            svg {\n              display: block;\n            }\n\n            .chat_bg {\n              clip-path: polygon(42.223% 9.81%, 42.223% 9.81%, 43.719% 9.159%, 45.255% 8.652%, 46.82% 8.29%, 48.405% 8.072%, 50% 8%, 51.595% 8.072%, 53.18% 8.29%, 54.745% 8.652%, 56.281% 9.159%, 57.777% 9.81%, 67.417% 14.548%, 77.079% 19.242%, 77.079% 19.242%, 78.514% 20.022%, 79.862% 20.925%, 81.117% 21.94%, 82.273% 23.062%, 83.323% 24.282%, 84.261% 25.592%, 85.082% 26.985%, 85.779% 28.452%, 86.346% 29.987%, 86.777% 31.58%, 89.137% 42.181%, 91.543% 52.772%, 91.543% 52.772%, 91.837% 54.397%, 91.982% 56.029%, 91.982% 57.658%, 91.838% 59.274%, 91.553% 60.868%, 91.128% 62.429%, 90.567% 63.949%, 89.871% 65.416%, 89.042% 66.823%, 88.083% 68.158%, 81.385% 76.64%, 74.725% 85.153%, 74.725% 85.153%, 73.656% 86.399%, 72.489% 87.531%, 71.233% 88.546%, 69.898% 89.44%, 68.493% 90.207%, 67.025% 90.844%, 65.504% 91.346%, 63.939% 91.709%, 62.339% 91.928%, 60.712% 92%, 50% 91.976%, 39.288% 92%, 39.288% 92%, 37.661% 91.928%, 36.061% 91.709%, 34.496% 91.346%, 32.975% 90.844%, 31.507% 90.207%, 30.102% 89.44%, 28.767% 88.546%, 27.511% 87.531%, 26.344% 86.399%, 25.275% 85.153%, 18.615% 76.64%, 11.917% 68.158%, 11.917% 68.158%, 10.958% 66.823%, 10.13% 65.416%, 9.433% 63.949%, 8.872% 62.429%, 8.448% 60.868%, 8.162% 59.274%, 8.018% 57.658%, 8.018% 56.029%, 8.163% 54.397%, 8.456% 52.772%, 10.863% 42.181%, 13.224% 31.58%, 13.224% 31.58%, 13.654% 29.987%, 14.221% 28.452%, 14.918% 26.985%, 15.739% 25.592%, 16.677% 24.282%, 17.728% 23.062%, 18.883% 21.94%, 20.138% 20.925%, 21.486% 20.022%, 22.921% 19.242%, 32.583% 14.548%, 42.223% 9.81%);\n            }\n          }\n        }\n\n        //.chat_bg {\n        //  height: 100px;\n        //  width: 100px;\n        //  margin: 0 auto;\n        //}\n      }\n\n      .chat_icon_role {\n        position: absolute;\n        right: 20px;\n        top: -10px;\n        width: 28px;\n        height: 28px;\n      }\n\n      .chat_user_popup_rating {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin: 12px 0;\n      }\n\n      .chat_user_popup_rating_buttons {\n        display: flex;\n        align-items: center;\n        margin-left: 10px;\n        border-radius: 4px;\n        padding: 6px 16px;\n        font-size: 12px;\n        line-height: 16px;\n\n        .chat_icon {\n          width: 16px;\n          height: 16px;\n        }\n      }\n\n      .rating_button {\n        display: flex;\n        justify-content: center;\n\n        .rating_number {\n          padding: 0 8px;\n        }\n      }\n\n      .rating_button + .rating_button {\n        margin-left: 10px;\n      }\n\n      .chat_user_level {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding-top: 3px;\n        padding-bottom: 3px;\n\n        i {\n          margin-right: 5px;\n\n          &::before {\n            font-size: 15px;\n          }\n        }\n\n        .chat_user_level_label_old {\n          border: 1px solid;\n          border-radius: 6px;\n          padding: 1px 30px;\n          font-size: 10px;\n\n          &.stranger {\n            border-color: rgb(189 186 181);\n            background-color: rgb(189 186 181 / 30%);\n          }\n\n          &.newbie {\n            border-color: rgb(198 38 104);\n            background-color: rgb(198 38 104 / 30%);\n          }\n\n          &.beginner {\n            border-color: rgb(77 144 42);\n            background-color: rgb(77 144 42 / 30%);\n          }\n\n          &.experienced {\n            border-color: rgb(198 193 38);\n            background-color: rgb(198 193 38 / 30%);\n          }\n\n          &.master {\n            border-color: rgb(198 134 38);\n            background-color: rgb(198 134 38 / 30%);\n          }\n\n          &.pro {\n            border-color: rgb(129 38 198);\n            background-color: rgb(129 38 198 / 30%);\n          }\n\n          &.guru {\n            border-color: rgb(38 102 196);\n            background-color: rgb(38 102 196 / 30%);\n          }\n        }\n\n        .chat_user_level_label {\n          border: 1px solid;\n          border-radius: 6px;\n          padding: 1px 30px;\n          font-size: 10px;\n\n          &.level_beginner {\n            border-color: rgb(77 144 42);\n            background-color: rgb(77 144 42 / 30%);\n          }\n\n          &.level_guru {\n            border-color: rgb(198 134 38);\n            background-color: rgb(198 134 38 / 30%);\n          }\n\n          &.level_master {\n            border-color: rgb(38 102 196);\n            background-color: rgb(38 102 196 / 30%);\n          }\n\n          &.level_vip,\n          &.level_vip_elite {\n            border-color: rgb(249 229 150);\n            background-color: rgb(249 229 150 / 30%);\n          }\n        }\n      }\n    }\n  }\n\n  /* Chat info popup */\n  .chat_info {\n    .chat_info_header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      margin-bottom: 10px;\n\n      .chat_info_image {\n        position: relative;\n        margin-right: 15px;\n\n        .chat_bg {\n          border-radius: 100%;\n          width: var(--chat-room-info-avatar-size);\n          height: var(--chat-room-info-avatar-size);\n        }\n      }\n\n      .chat_info_text {\n        flex: 1;\n\n        h4 {\n          font-size: 16px;\n\n          .chat_icon {\n            width: 16px;\n            height: 16px;\n          }\n        }\n\n        p {\n          display: flex;\n          align-items: center;\n\n          .chat_icon_user {\n            vertical-align: middle;\n            margin-right: 5px;\n            width: 12px;\n            height: 12px;\n          }\n        }\n      }\n    }\n\n    .chat_info_block {\n      margin-top: 15px;\n    }\n  }\n\n  /* RTL Version */\n  &.rtl {\n    direction: rtl;\n\n    .chat_form {\n      .chat_file_upload_wrapper {\n        .chat_file_upload_description {\n          margin-right: 15px;\n          margin-left: 0;\n        }\n      }\n    }\n\n    .chat_main {\n      .chat_main_header {\n        .chat_main_actions {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 20px 20px 0;\n\n          .mark_read_button {\n            margin-right: 10px;\n            margin-left: 0;\n          }\n\n          .chat_main_search {\n            .chat_icon {\n              left: 20px;\n              right: auto;\n            }\n          }\n        }\n      }\n    }\n\n    .chat_search {\n      .chat_search_header {\n        .chat_search_field {\n          .chat_icon {\n            left: 10px;\n            right: auto;\n          }\n        }\n      }\n    }\n\n    .chat_tabs_nav {\n      li {\n        margin-right: 0;\n        margin-left: 10px;\n\n        &:last-child {\n          margin-left: 0;\n        }\n      }\n    }\n\n    .chat_list {\n      padding: 0 3px 0 10px;\n\n      .chat_list_inner {\n        &.chat_list_promo {\n          .chat_list_promo_label {\n            left: -1px;\n            right: auto;\n            border-top-left-radius: 7px;\n            border-top-right-radius: 0;\n            border-bottom-right-radius: 3px;\n            border-bottom-left-radius: 0;\n          }\n        }\n      }\n\n      .chat_list_content {\n        margin-right: 10px;\n        margin-left: 0;\n        direction: rtl;\n\n        .chat_icon {\n          margin-right: 5px;\n          margin-left: 0;\n        }\n      }\n\n      .chat_icon_pinned {\n        left: 12px;\n        right: auto;\n      }\n    }\n\n    .chat_room {\n      .chat_room_header {\n        .chat_dropdown {\n          direction: rtl;\n\n          a {\n            .chat_icon {\n              margin-right: 18px;\n              margin-left: 25px;\n            }\n          }\n        }\n\n        .chat_room_back {\n          .chat_icon svg {\n            transform: rotate(180deg);\n          }\n        }\n\n        .chat_room_header_menu_trigger {\n          margin-right: 10px;\n          margin-left: 0;\n        }\n\n        .chat_room_header_text {\n          h4 {\n            .chat_icon {\n              margin-right: 5px;\n              margin-left: 0;\n            }\n          }\n        }\n      }\n\n      .chat_room_content {\n        .chat_message {\n          .chat_message_wrapper {\n            margin-right: 10px;\n            margin-left: 0;\n          }\n        }\n      }\n\n      .chat_room_footer {\n        .chat_message_form {\n          form {\n            .chat_message_form_field {\n              margin: 0 70px 0 35px;\n            }\n\n            .chat_message_form_options {\n              left: auto;\n              right: 10px;\n            }\n\n            .chat_message_form_button {\n              left: 10px;\n              right: auto;\n            }\n          }\n        }\n\n        .chat_message_text_trigger {\n          left: auto;\n          right: 100%;\n        }\n      }\n    }\n\n    .chat_message_text {\n      blockquote {\n        border-right-width: 2px;\n        border-left-width: 0;\n        padding-right: 6px;\n        padding-left: 0;\n      }\n\n      .chat_message_rating {\n        .rating_button {\n          margin-right: 7px;\n          margin-left: 0;\n        }\n      }\n    }\n\n    .chat_members {\n      .chat_members_content {\n        margin-right: 10px;\n        margin-left: 0;\n      }\n    }\n\n    .chat_user_popup {\n      .chat_popup_inner {\n        .chat_user_popup_rating_buttons {\n          margin-right: 10px;\n          margin-left: 0;\n        }\n\n        .chat__user-level i {\n          margin-right: 0;\n          margin-left: 5px;\n        }\n\n        .rating_button + .rating_button {\n          margin-right: 10px;\n          margin-left: 0;\n        }\n\n        .chat_popup_links {\n          text-align: right;\n        }\n      }\n    }\n  }\n\n  .s-chat_open & {\n    display: block;\n  }\n}\n\n.chat-tooltip {\n  z-index: 90;\n  margin: 3px;\n  padding: 10px;\n  font-size: 12px;\n}\n\n@media (min-width: 768px) {\n  .chat_app .chat {\n    left: auto;\n    top: 0;\n    bottom: 0;\n    width: 360px;\n  }\n}\n\n// Внутрении страницы, чат открыт\nbody.s-chat_open:not(.is-chart):not(.#{default.$m-version-class-name}) {\n  .chat_app {\n    position: fixed;\n    top: map.get(default.$header, height);\n    bottom: 0;\n    transform: translateX(settings(sidebar, width));\n    transition: transform 0.3s;\n  }\n\n  &.minimize-left-sidebar .chat_app {\n    transform: translateX(settings(sidebar, minimizeWidth));\n  }\n}\n", "@mixin NotoSans {\n  font-family: \"Noto Sans\", \"Arial\", sans-serif;\n}\n", "@use \"@mixins/settings\" as *;\n\n$-message-background-color: #2a3145; // фон сообщений юзеров\n$-message-background-color-vip: #1f222c; // фон сообщений юзеров VIP\n$-color-text-white: #fff;\n$-color-text-blue: #8ea5c0;\n\n.theme-dark-blue {\n  .chat_app {\n    /* Form elements */\n    select,\n    textarea,\n    input {\n      border: 1px solid #44506a;\n      color: #8ea5c0;\n      background-color: #1d2130;\n    }\n\n    label {\n      color: #8ea5c0;\n    }\n\n    .checkbox {\n      & > input {\n        &:checked + span::before {\n          border-color: #009af9;\n          background-color: #314463;\n        }\n      }\n\n      & > span {\n        &::before {\n          border-color: #454a56;\n          background-color: #222739;\n        }\n      }\n    }\n\n    .v-select {\n      .vs__dropdown-toggle {\n        border: 1px solid #44506a;\n        background: #1d2130;\n\n        .vs__selected-options {\n          .vs__selected {\n            color: #fff;\n          }\n        }\n\n        .vs__actions {\n          .vs__open-indicator {\n            fill: #8ea5c0;\n          }\n        }\n      }\n\n      .vs__dropdown-menu {\n        border: none;\n        color: #fff;\n        background: #262c41;\n\n        .vs__dropdown-option--highlight {\n          color: #fff;\n          background: #364059;\n        }\n      }\n    }\n\n    ::-webkit-input-placeholder {\n      color: #6d788a;\n      opacity: 1;\n    }\n\n    ::-moz-placeholder {\n      color: #6d788a;\n      opacity: 1;\n    }\n\n    :-ms-input-placeholder {\n      color: #6d788a;\n      opacity: 1;\n    }\n\n    :-moz-placeholder {\n      color: #6d788a;\n      opacity: 1;\n    }\n\n    a {\n      color: #8ea5c0;\n\n      &:hover,\n      &:focus {\n        color: #b6c8dd;\n      }\n    }\n\n    /* Wrapper */\n    .chat {\n      .chat_wrapper {\n        background-color: #222636;\n      }\n    }\n\n    .label {\n      display: inline-block;\n    }\n\n    .label-primary {\n      background-color: #337ab7;\n    }\n\n    /* Form  */\n    .chat_form {\n      color: #8ea5c0;\n\n      .chat_field_label {\n        .chat_icon {\n          fill: #44506a;\n\n          &:hover {\n            fill: #8ea5c0;\n          }\n        }\n      }\n\n      /* File upload */\n      .chat_file_upload_wrapper {\n        .chat_file_upload_area {\n          background: #1D2130;\n          border: none;\n        }\n\n        .chat_photo_crop_area {\n          .cr-slider {\n            &::-webkit-slider-thumb {\n              background: #7E91A7;\n            }\n          }\n        }\n      }\n    }\n\n    /* Buttons */\n    .chat_btn {\n\n      border: transparent;\n      background-color: #293145;\n      color: #FFFFFF;\n\n      &:hover,\n      &:focus {\n        background-color: #3d475d;\n      }\n\n      &.chat_btn_action {\n        background-color: rgba(2, 91, 68, 0.15);\n        border: 1px solid #025B44;\n        color: #FFFFFF;\n\n        &:hover {\n          background-color: #025B44;\n        }\n      }\n\n      &.chat_btn_danger {\n        background-color: rgba(129, 42, 45, 0.15);\n        border: 1px solid #812A2D;\n        color: #FFFFFF;\n\n        &:hover {\n          background-color: #812A2D;\n        }\n      }\n\n      &.chat_btn_info {\n        background-color: #286090;\n        border: 1px solid #286090;\n        color: #FFFFFF;\n      }\n\n      &.chat_btn_trans {\n        border: 1px solid #454a56;\n        color: #fff;\n        background-color: #1d2130;\n\n        .chat_icon {\n          fill: #fff;\n        }\n\n        &:hover {\n          border-color: #009af9;\n        }\n\n        &.s-active {\n          border-color: #009af9;\n          background-color: #314463;\n        }\n      }\n    }\n\n    .chat_btn_icon {\n      .chat_icon {\n        fill: #8EA5C0;\n      }\n    }\n\n    /* Scroll */\n    .os-theme-dark,\n    .os-theme-light {\n      > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle {\n        background-color: #454a56;\n      }\n\n      > .os-scrollbar.os-scrollbar-unusable,\n      > .os-scrollbar > .os-scrollbar-track {\n        background-color: #1d2130;\n      }\n    }\n\n    /* Icons */\n    .chat_icon {\n      fill: #8ea5c0;\n    }\n\n    /* Main */\n    .chat_main {\n      .chat_main_header {\n        background-color: #1E2232;\n\n        .chat_main_actions {\n          .chat_btn {\n            &.s-active {\n              .chat_icon {\n                fill: #fff;\n              }\n            }\n          }\n\n          .mark_read_button {\n            .chat_icon {\n              fill: #fff;\n              opacity: 0.35;\n            }\n\n            span {\n              opacity: 0.35;\n            }\n\n            &.s-active {\n              span, .chat_icon {\n                opacity: 1;\n              }\n            }\n          }\n        }\n      }\n    }\n\n    /* Search */\n\n    /* Chat create */\n    .chat_create {\n      .chat_create_header {\n        background-color: #1E2232;\n\n        h4 {\n          color: #FFFFFF;\n        }\n      }\n    }\n\n    /* Room list */\n    .chat_tab_header {\n      background-color: #2a3144;\n\n      .chat_title h4 {\n        color: #fff;\n      }\n\n      .chat_dropdown a {\n        color: #8ea5c0;\n\n        &:hover {\n          color: #fff;\n        }\n      }\n    }\n\n    /* Room list item */\n    .chat_list {\n      color: #8EA5C0;\n      border-bottom-color: #2A3144;\n\n      &:hover {\n        background-color: #252B3C;\n      }\n\n      &.chat_room_vip {\n        .chat_list_inner {\n          .chat_list_content h4 {\n            color: #F9E596;\n          }\n        }\n      }\n\n      .chat_list_inner {\n        &.chat_list_promo {\n          border: 1px solid #44506A;\n          background: rgba(68, 80, 106, .1);\n\n          .chat_list_promo_label {\n            background: #44506A;\n            color: #FFFFFF;\n          }\n\n          .chat_list_content {\n            > p {\n              color: #FFFFFF;\n            }\n          }\n        }\n\n        .chat_list_content h4 {\n          color: #fff;\n        }\n      }\n    }\n\n    /* Room room */\n    .chat_room {\n\n      .chat_room_header_pinned {\n        background: #252B3C;\n        color: #8ea5c0;\n\n        div.text {\n          color: #FFFFFF;\n\n          a {\n            color: #3797D3;\n          }\n        }\n\n        .chat_icon {\n          fill: #8EA5C0;\n        }\n      }\n\n      .chat_room_header {\n        background-color: #1E2232;\n\n        &.chat_room_vip {\n          .chat_room_header_text {\n            h4 {\n              color: #F9E596;\n            }\n          }\n        }\n\n        .chat_icon {\n          .chat_icon {\n            fill: #8ea5c0;\n          }\n\n          &:hover .chat_icon {\n            fill: #fff;\n          }\n        }\n\n        .chat_dropdown {\n          background-color: #222636;\n\n          li {\n            border-bottom-color: #2A3144;\n\n            &:hover {\n              background-color: #293145;\n            }\n          }\n\n          a {\n            fill: #8ea5c0;\n\n            .chat_icon {\n              fill: #8ea5c0;\n            }\n          }\n        }\n      }\n\n\n      .chat_room_header_text {\n        h4 {\n          color: #fff;\n        }\n\n        p {\n          color: #8e9aac;\n        }\n      }\n\n      .chat_room_header_text .chat_icon_user {\n        fill: #8e9aac;\n      }\n\n      .chat_room_content {\n        .disabled {\n          background-color: rgba(25, 27, 41, 0.7);\n        }\n      }\n\n      .chat_room_footer {\n        .chat_message_form {\n          background-color: #2A3144;\n\n          .chat_message_title {\n            color: #CCCCCC;\n          }\n\n          .chat_message_rating {\n            background-color: #252a3b;\n          }\n\n          .chat_trigger,\n          .chat_message_text_trigger {\n            .chat_icon {\n              fill: #8e9aac;\n            }\n\n            &:hover {\n              .chat_icon {\n                fill: #d3d7de;\n              }\n            }\n          }\n        }\n\n        .chat_room_emotions {\n          background-color: #293145;\n        }\n\n        .chat_room_emotions_label {\n          color: #8ea5c0;\n        }\n      }\n\n      .chat_mention_button {\n        .chat_icon {\n          fill: #8E9AAC;\n        }\n\n        &:hover .chat_icon {\n          fill: #3B4B68;\n        }\n      }\n\n    }\n\n    /* Chat message */\n    .chat_message {\n      &.chat_message_info {\n        p {\n          color: #ccc;\n          background-color: $-message-background-color;\n        }\n      }\n\n      &.level_vip,\n      &.level_vip_elite {\n        .chat_message_header {\n          a.chat_name_text {\n            color: #f9e596;\n          }\n\n          .chat_date {\n            color: #f9e596;\n          }\n        }\n\n        .chat_message_text {\n          background-color: $-message-background-color-vip;\n\n          p {\n            color: #e8e2d6;\n          }\n\n          blockquote {\n            border-color: #f9e596;\n\n            h5 a {\n              color: #c1b571;\n            }\n          }\n        }\n\n        .chat_avatar_circle {\n          .chat_avatar_crown {\n            background-color: #222636;\n          }\n        }\n      }\n\n      &.highlight {\n        background-color: #354156;\n      }\n\n      .chat_message_header {\n        a.chat_name_text {\n          color: #799aac;\n        }\n\n        a:hover {\n          color: #b6c8dd;\n        }\n\n        .chat_date {\n          color: #8ea5c0;\n        }\n      }\n    }\n\n    /* Chat message text */\n    .chat_message_text {\n      color: #ccc;\n      background-color: $-message-background-color;\n\n      &.chat_message_text_blue {\n        background-color: $-message-background-color;\n      }\n\n      p {\n        color: #ccc;\n      }\n\n      a:hover {\n        color: #b6c8dd;\n      }\n\n      blockquote {\n        border-color: #3498db;\n\n        h5 a {\n          color: #3498db;\n        }\n      }\n\n      .chat_message_top_trader {\n        background-color: #21273d;\n      }\n\n      .chat_message_rating {\n        .rating_button {\n\n          .chat_icon {\n            fill: #ccc;\n          }\n\n          a:hover {\n            fill: #7ede9e;\n          }\n\n          &.marked .chat_icon {\n            fill: #3498db;\n          }\n        }\n      }\n    }\n\n    /* Members item */\n    .chat_members {\n      .chat_members_content {\n        .chat_members_content_top {\n          color: #ffffff;\n        }\n\n        .chat_members_content_bottom {\n          color: #8EA5C0;\n        }\n      }\n\n      &:hover, &.highlight {\n        background: rgba(#2B476F, 50%);\n      }\n\n      &.selected {\n        background: #2B476F;\n        border: 1px solid #009AF9;\n      }\n\n      &.editors {\n        .chat_members_content {\n          .name {\n            color: #FFFFFF;\n          }\n\n          .role {\n            color: #8EA5C0;\n          }\n        }\n      }\n    }\n\n    /* Chat promotion */\n    .chat_promotion {\n      .promotion_select_option {\n        .position {\n          color: $-color-text-white;\n        }\n\n        .price {\n          color: $-color-text-blue;\n        }\n\n        &.disabled {\n          .position, .price {\n            color: rgba($-color-text-blue, 50%);\n          }\n        }\n      }\n\n      .chat_promotion_info {\n        background: #1D2130;\n\n        .title {\n          color: $-color-text-blue;\n        }\n\n        .amount {\n          .value {\n            color: #32AC41;\n          }\n        }\n\n        .info {\n          .value {\n            color: $-color-text-white;\n          }\n        }\n      }\n    }\n\n    /* Chat popup */\n    .chat_popup {\n      background-color: rgba(#191b29, 70%);\n\n      .chat_popup_inner {\n        color: #ffffff;\n        background-color: #222636;\n\n        .chat_popup_error {\n          color: #f96868 !important;\n        }\n\n        .chat_popup_title {\n          color: #FFFFFF;\n          border-bottom-color: #2A3144;\n\n          .description {\n            color: #8EA5C0;\n          }\n        }\n\n        .chat_popup_content {\n          color: #FFFFFF;\n        }\n\n        .chat_popup_bottom {\n          border-top-color: #2A3144;\n        }\n      }\n    }\n\n    /* Chat menu popup */\n    .chat_menu_popup {\n      .chat_popup_inner {\n        ul,\n        .chat_message {\n          background-color: #222636;\n        }\n\n        .chat_message {\n          border-color: #3498db;\n        }\n\n        li {\n          border-top-color: #2a3144;\n\n          a {\n            color: #8ea5c0;\n\n            .chat_icon {\n              fill: #8ea5c0;\n            }\n          }\n\n          &:hover:not(.chat_menu_rating):not(.chat_menu_message_hide) {\n            background-color: #293145;\n\n            a {\n              color: #fff;\n\n              .chat_icon {\n                fill: #fff;\n              }\n            }\n          }\n        }\n\n        .chat_menu_message_hide {\n          a {\n            background-color: #293145;\n          }\n        }\n      }\n    }\n\n    /* Chat user popup */\n    .chat_user_popup {\n      input {\n        background-color: #303549;\n      }\n\n      .chat_popup_inner {\n        box-shadow: 0 5px 20px rgba(0 0 0 / 15%);\n        color: #ccc;\n\n        h4 {\n          color: #fff;\n        }\n\n        .chat_user_popup_rating {\n          color: #fff;\n        }\n\n        .chat_user_popup_rating_buttons {\n          background-color: #2b304a;\n\n          .chat_icon {\n            fill: #8e9aac;\n          }\n        }\n\n        .chat_popup_links {\n          li {\n            border-bottom-color: #2A3144;\n\n            &:hover {\n              background-color: #293145;\n            }\n          }\n\n          a {\n            color: #8EA5C0;\n          }\n        }\n\n        .chat_avatar_circle {\n          .chat_avatar_crown {\n            background-color: #222636;\n          }\n        }\n      }\n    }\n\n    /* Chat info popup */\n    .chat_info {\n      .chat_info_header {\n        p {\n          color: #8EA5C0;\n\n          .chat_icon_user {\n            fill: #8EA5C0;\n          }\n        }\n      }\n\n      .chat_info_block {\n        .chat_info_title {\n          color: #8EA5C0;\n        }\n\n        .chat_info_text {\n          color: #FFFFFF;\n        }\n      }\n    }\n  }\n}\n\n.chat-tooltip {\n  border: 1px solid #2c3245;\n  border-radius: 5px;\n  color: #fff;\n  background: #1f2436;\n}\n", "@use \"sass:color\";\n\n$color-text-blue: #314e70;\n$color-text-light: #8ea5c0;\n\n.theme-light {\n  .chat_app {\n    /* Form elements */\n    input,\n    select,\n    textarea {\n      color: #555;\n      background-color: #fff;\n      border: 1px solid #dadee3;\n    }\n\n    label {\n      color: #8EA5C0;\n    }\n\n    .checkbox {\n      & > input {\n        &:checked + span::before {\n          border-color: #406C9D;\n          background-color: #5181B8;\n        }\n      }\n\n      & > span {\n        &::before {\n          border-color: #E4EAF1;\n          background-color: #fff;\n        }\n      }\n    }\n\n    .v-select {\n      .vs__dropdown-toggle {\n        border: 1px solid #E4EAF1;\n        background: #FFFFFF;\n\n        .vs__selected-options {\n          .vs__selected {\n            color: #314E70;\n          }\n        }\n\n        .vs__actions {\n          .vs__open-indicator {\n            fill: #8EA5C0;\n          }\n        }\n      }\n\n      .vs__dropdown-menu {\n        background: #FFFFFF;\n        border: none;\n        color: #314E70;\n\n        .vs__dropdown-option--highlight {\n          color: #314E70;\n          background: #E1E5EB;\n        }\n      }\n    }\n\n    ::-webkit-input-placeholder {\n      color: #8E9AAC;\n      opacity: 1;\n    }\n\n    ::-moz-placeholder {\n      color: #8E9AAC;\n      opacity: 1;\n    }\n\n    :-ms-input-placeholder {\n      color: #8E9AAC;\n      opacity: 1;\n    }\n\n    :-moz-placeholder {\n      color: #8E9AAC;\n      opacity: 1;\n    }\n\n    a {\n      color: #8ea5c0;\n\n      &:hover,\n      &:focus {\n        color: #1e3662;\n      }\n    }\n\n    /* Wrapper */\n    .chat {\n      .chat_wrapper {\n        background-color: #F7F8FA;\n      }\n    }\n\n    /* Form  */\n    .chat_form {\n      color: #314e70;\n\n      .chat_field_label {\n        .chat_icon {\n          fill: #849BB7;\n\n          &:hover {\n            fill: #8EA5C0;\n          }\n        }\n      }\n\n      /* File upload */\n      .chat_file_upload_wrapper {\n        .chat_file_upload_area {\n          border: 1px solid #e4eaf1;\n          background-color: #fff;\n\n          span {\n            color: #c7d2df;\n          }\n\n          .chat_icon {\n            fill: #c7d2df;\n          }\n        }\n\n        .chat_photo_crop_area {\n          .cr-slider {\n            &::-webkit-slider-thumb {\n              background: #8ea5c0;\n            }\n          }\n        }\n\n        .chat_btn {\n          background-color: #3b628f;\n          transition: background-color 0.3s;\n\n          &:hover {\n            background-color: #4a76a8;\n          }\n        }\n      }\n    }\n\n    /* Buttons */\n    .chat_btn {\n      color: #fff;\n      background-color: #5181b8;\n\n      &:hover,\n      &:focus {\n        background-color: #5b88bd;\n      }\n\n      &.chat_btn_action {\n        background-color: #5CB85C;\n        color: #FFFFFF;\n      }\n\n      &.chat_btn_danger {\n        background-color: #D9534F;\n        color: #FFFFFF;\n      }\n\n      &.chat_btn_info {\n        background-color: #286090;\n        color: #FFFFFF;\n      }\n\n      &.chat_btn_trans {\n        border: 1px solid #c2cedc;\n        color: #8ea5c0;\n        background-color: #e5ebf1;\n\n        .chat_icon {\n          fill: #8EA5C0;\n        }\n\n        &:hover {\n          border-color: #406c9d;\n        }\n\n        &.s-active {\n          border-color: #406c9d;\n          color: #fff;\n          background-color: #5181b8;\n        }\n      }\n    }\n\n    .chat_btn_icon {\n      .chat_icon {\n        fill: #FFFFFF;\n      }\n    }\n\n    /* Scroll */\n    .os-theme-dark,\n    .os-theme-light {\n      > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle {\n        background-color: #c2cedc;\n      }\n\n      > .os-scrollbar.os-scrollbar-unusable,\n      > .os-scrollbar > .os-scrollbar-track {\n        background-color: #e5ebf1;\n      }\n    }\n\n    /* Icons */\n    .chat_icon {\n      fill: #8ea5c0;\n    }\n\n\n    /* Main */\n    .chat_main {\n      .chat_main_header {\n        background-color: #ECEFF3;\n\n        .chat_main_actions {\n          .mark_read_button {\n            opacity: 0.35;\n\n            &.s-active {\n              opacity: 1;\n            }\n          }\n        }\n      }\n    }\n\n    /* Search */\n\n    /* Chat create */\n    .chat_create {\n      .chat_create_header {\n        background-color: #ECEFF3;\n\n        h4 {\n          color: #314E70;\n        }\n      }\n    }\n\n    /* Room list */\n    .chat_tab_header {\n      background-color: #e1e5eb;\n\n      .chat_title h4 {\n        color: #314e70;\n      }\n\n      .chat_dropdown a {\n        color: #314e70;\n\n        &:hover {\n          color: #0f1821;\n        }\n      }\n    }\n\n    /* Room list item */\n    .chat_list {\n      border-bottom-color: #c5d0db;\n      color: #8ea5c0;\n\n      &:hover {\n        background-color: #EFF2F7;\n      }\n\n      &.chat_room_vip {\n        .chat_list_inner {\n          .chat_list_content h4 {\n            color: #AF906C;\n          }\n        }\n      }\n\n      .chat_list_inner {\n        &.chat_list_promo {\n          border: 1px solid #8EA5C0;\n          background: rgba(81, 129, 184, .05);\n\n          .chat_list_promo_label {\n            background: #8EA5C0;\n            color: #FFFFFF;\n          }\n\n          .chat_list_content {\n            > p {\n              color: #2A3144;\n            }\n          }\n        }\n\n        .chat_list_content h4 {\n          color: #314e70;\n        }\n      }\n    }\n\n    /* Room header */\n    .chat_room {\n\n      .chat_room_header_pinned {\n        background: #FCFCFC;\n        color: #314e70;\n        box-shadow: 0 4px 5px -2px #ebebeb;\n\n        div.text {\n          color: #314E70;\n\n          a {\n            color: #3797D3;\n          }\n        }\n\n        .chat_icon {\n          fill: #8EA5C0;\n        }\n      }\n\n      .chat_room_header {\n        background-color: #ECEFF3;\n\n        &.chat_room_vip {\n          .chat_room_header_text {\n            h4 {\n              color: #AF906C;\n            }\n          }\n        }\n\n        .chat_back .chat_icon {\n          fill: #8ea5c0;\n        }\n\n        .chat_dropdown {\n          background-color: #F7F8FA;\n\n          li {\n            border-bottom-color: #DCE1E7;\n\n            &:hover {\n              background-color: #E6ECF3;\n            }\n          }\n\n          a {\n            color: #314E70;\n\n            .chat_icon {\n              fill: #314E70;\n            }\n          }\n        }\n      }\n\n      .chat_room_header_text {\n        color: #314e70;\n\n        > p {\n          color: #8ea5c0;\n\n          .chat_icon {\n            color: #8ea5c0;\n          }\n        }\n      }\n\n      .chat_room_header_text .chat_icon_user {\n        fill: #8e9aac;\n      }\n\n      .chat_room_content {\n        .disabled {\n          background-color: rgba(59, 75, 104, 0.25)\n        }\n      }\n\n      /* Room footer */\n      .chat_room_footer {\n        .chat_message_form {\n          background-color: #ECEFF3;\n\n          .chat_message_title {\n            color: #314E70;\n          }\n\n          .chat_message_rating {\n            background-color: #FFFFFF;\n          }\n\n          .chat_trigger, .chat_message_text_trigger {\n            .chat_icon {\n              fill: #8EA5C0;\n            }\n\n            &:hover {\n              .chat_icon {\n                fill: color.adjust(#8ea5c0, $lightness: 10%);\n              }\n            }\n          }\n        }\n\n        .chat_room_emotions {\n          box-shadow: 0 5px 15px rgba(0 0 0 / 10%);\n          background-color: #fff;\n        }\n      }\n\n      .chat_mention_button {\n        .chat_icon {\n          fill: #8e9aac;\n        }\n\n        &:hover .chat_icon {\n          fill: #3b4b68;\n        }\n      }\n    }\n\n    /* Chat message */\n    .chat_message {\n      &.chat_message_info {\n        p {\n          color: #314E70;\n          background-color: #FFFFFF;\n        }\n      }\n\n      &.level_vip,\n      &.level_vip_elite {\n        .chat_message_header {\n          a.chat_name_text {\n            color: #AF906C;\n          }\n\n          .chat_date {\n            color: #AF906C;\n          }\n        }\n\n        .chat_message_text {\n          background-color: #FFFFFF;\n\n          p {\n            color: #AF906C;\n          }\n\n          blockquote {\n            border-color: #AF906C;\n\n            h5 a {\n              color: #AF906C;\n            }\n          }\n        }\n      }\n\n      .chat_avatar_circle {\n        .chat_avatar_crown {\n          background-color: #F7F8FA;\n        }\n      }\n\n      &.highlight {\n        background-color: #DCE7F1;\n      }\n\n      .chat_message_header {\n        a.chat_name_text {\n          color: #799aac;\n        }\n\n        a:hover {\n          color: #1e3662;\n        }\n\n        .chat_date {\n          color: #8EA5C0;\n        }\n      }\n    }\n\n    /* Room message text */\n    .chat_message_text {\n      background-color: #FFFFFF;\n\n      &.chat_message_text_blue {\n        background-color: #FFFFFF;\n      }\n\n      p {\n        color: #314E70;\n      }\n\n      a:hover {\n        color: #1e3662;\n      }\n\n      blockquote {\n        border-color: #5181B8;\n\n        h5 a {\n          color: #5181B8;\n        }\n      }\n\n      .chat_message_top_trader {\n        background-color: #f3f3f5;\n\n        a:hover {\n          color: #1e3662;\n        }\n      }\n\n      .chat_message_rating {\n        .rating_button {\n\n          .chat_icon {\n            fill: #3498db;\n          }\n\n          a:hover {\n            fill: #1e3662;\n          }\n\n          &.marked .chat_icon {\n            fill: #3ec160;\n          }\n        }\n      }\n    }\n\n    .chat_members {\n      .chat_members_content {\n        .chat_members_content_top {\n          color: #314E70;\n        }\n\n        .chat_members_content_bottom {\n          color: #8EA5C0;\n        }\n      }\n\n      &:hover, &.highlight {\n        background: #ECEFF3;\n      }\n\n      &.selected {\n        background: #ECEFF3;\n        border: 1px solid #C2CEDC;\n      }\n\n      &.editors {\n        .chat_members_content {\n          .name {\n            color: #314E70;\n          }\n\n          .role {\n            color: #8EA5C0;\n          }\n        }\n      }\n    }\n\n    /* Chat promotion */\n    .chat_promotion {\n      .promotion_select_option {\n        .position {\n          color: $color-text-blue;\n        }\n\n        .price {\n          color: $color-text-blue;\n        }\n\n        &.disabled {\n          .position, .price {\n            color: rgba($color-text-blue, 50%);\n          }\n        }\n      }\n\n      .chat_promotion_info {\n        background: #FFFFFF;\n        border: 1px solid #E4EAF1;\n\n        .title {\n          color: $color-text-light;\n        }\n\n        .amount {\n          .value {\n            color: #32AC41;\n          }\n        }\n\n        .info {\n          .value {\n            color: $color-text-blue;\n          }\n        }\n      }\n    }\n\n    /* Chat popup */\n    .chat_popup {\n      background-color: rgba(#3b4b68, 25%);\n\n      .chat_popup_inner {\n        color: #314E70;\n        background-color: #f7f8fa;\n\n        .chat_popup_error {\n          color: #e26161 !important;\n        }\n\n        .chat_popup_title {\n          color: #314E70;\n          border-bottom-color: #E4EAF1;\n\n          .description {\n            color: #8EA5C0;\n          }\n        }\n\n        .chat_popup_content {\n          color: #314E70;\n        }\n\n        .chat_popup_bottom {\n          border-top-color: #E4EAF1;\n        }\n      }\n    }\n\n    /* Chat menu popup */\n    .chat_menu_popup {\n      .chat_popup_inner {\n        ul,\n        .chat_message {\n          background-color: #f7f8fa;\n        }\n\n        .chat_message {\n          border-color: #3498db;\n        }\n\n        li {\n          border-top-color: #c1cad4;\n\n          a {\n            color: #314e70;\n\n            .chat_icon {\n              fill: #8ea5c0;\n            }\n          }\n\n          &:hover:not(.chat_menu_rating):not(.chat_menu_message_hide) {\n            background-color: rgba(#e1e5ec, 50%);\n          }\n        }\n\n        .chat_menu_message_hide {\n          a {\n            color: #fff;\n            background-color: #5181b8;\n\n            .chat_icon {\n              fill: #fff;\n            }\n          }\n        }\n\n        .chat_menu_rating > a {\n          border: 1px solid #c2cedc;\n          background-color: #e5ebf1;\n        }\n      }\n    }\n\n    /* Chat user popup */\n    .chat_user_popup {\n      .chat_popup_inner {\n        h4 {\n          color: #3b4b68;\n        }\n\n        .chat_user_popup_rating {\n          color: #314E70;\n        }\n\n        .chat_user_popup_rating_buttons {\n          background-color: #e2e6ea;\n\n          .chat_icon {\n            fill: #515a69;\n          }\n        }\n\n        .chat_popup_links {\n          li {\n            border-bottom-color: #DCE1E7;\n\n            &:hover {\n              background-color: #E6ECF3;\n            }\n          }\n\n          a {\n            color: #314E70;\n          }\n        }\n\n        .chat_avatar_circle {\n          .chat_avatar_crown {\n            background-color: #F7F8FA;\n          }\n        }\n      }\n    }\n\n    /* Chat info popup */\n    .chat_info {\n      .chat_info_header {\n        p {\n          color: #8EA5C0;\n\n          .chat_icon_user {\n            fill: #8EA5C0;\n          }\n        }\n      }\n\n      .chat_info_block {\n        .chat_info_title {\n          color: #8EA5C0;\n        }\n\n        .chat_info_text {\n          color: #314e70;\n        }\n      }\n    }\n  }\n}\n\n.chat-tooltip {\n  border: 1px solid #dfe7ee;\n  border-radius: 5px;\n  color: #314e70;\n  background: #fff;\n}\n"], "names": [], "sourceRoot": ""}