{"version": 3, "file": "css/desktop.theme-dark-blue.min.css", "mappings": "AAAA,8HAGE,+BAGF,8BAEE,mBADA,wBACA,CAGF,gCAEE,yBADA,iFACA,CAGF,gCAGE,mBAFA,yBACA,aACA,CAEA,uCAGE,yBAFA,qBACA,UACA,CCrBF,kCAGE,yBAFA,YACA,cAEA,uVASF,uHAEE,yBADA,UACA,CChBJ,wBACE,gBAEA,uCAEE,yBADA,aACA,CAGF,oCAEE,yBADA,kBACA,CAGF,mDACE,gBACA,yBACA,0BAIA,oDACE,yBCvBN,gCACE,yBAA0B,CAC1B,uBAAwB,CACxB,wBAAyB,CACzB,oCAAqC,CACrC,qCAAsC,CACtC,sCAAuC,CAEvC,wCACE,kBCJF,6BACE,cAOF,0DACE,WAKF,wNAME,+BAGF,oCACE,+BACA,wBACA,cAQE,gEACE,wBALE,CAcJ,+HAC<PERSON>,wBAdG,CAiBL,+DACE,wBAnBE,CAyBN,sDACE,yBAKN,+BACE,yBAIF,ghBAYE,kCAIF,4hBAYE,gCCjGF,sBAEE,yBADA,UACA,CAEA,mCACE,mECHF,gEAIE,yBAHA,yBACA,kBACA,aACA,CAEA,4IAKE,yBAHA,yBACA,kBACA,aACA,CAIJ,kDACE,yBAGE,uDAEE,yBADA,aACA,CAKA,6HACE,yBAKN,uDACE,sFAGF,wDACE,yBAMA,sJAIE,6BAFA,qBACA,aACA,CAMR,gEAEE,yBAGF,+BACE,yBAGF,kCAEE,8BADA,UACA,CClEF,kJAME,yBAFA,qBACA,uBACA,CAEA,oVAEE,qBACA,wCAIJ,8BAIE,yBAFA,yBACA,kBAEA,oFAKE,gJAEE,SAIF,mMAGE,6BACA,6BACA,wDACA,oBAEA,kBADA,oBAEA,oGAGF,uOAGE,oBAEA,kBADA,mBACA,CAIJ,iDAGE,yBAFA,SACA,aACA,CAGF,yCACE,yBC1DF,iEAEE,yBADA,aACA,CAEA,uEACE,yBAKF,sFACE,sCAGF,2DAEE,YACA,gBAFA,UAEA,CAEA,gEAEE,sBAEA,YACA,gBAJA,SAEA,UAEA,CAEA,sEACE,gBAQF,2RACE,sCAMR,gEAEE,mCADA,qFACA,CAEA,uJAEE,4BCnDN,+BAGE,+BAGE,2CAEE,8BADA,4BACA,CAEA,gDACE,WAKF,sDACE,gCChBN,uCACE,WAGF,uCACE,cCNJ,+BAEE,yBADA,oBACA,CAEA,iDAEE,yBADA,oBACA,CAEA,8GAGE,yBADA,oBACA,CCVJ,gDAIE,yBAHA,yBACA,oFACA,UACA,CCLJ,2BACE,yBAEA,8DAEE,sBADA,uFACA,CAGF,+DAEE,sBADA,uFACA,CCVJ,gCACE,qCAEA,sCACE,qCCCF,8HAEE,WAGF,4CAGE,yBAFA,qBACA,+FACA,CAGF,4CAGE,yBAFA,qBACA,2FACA,CChBF,0DAGE,yBAFA,yBACA,UACA,CAGF,oDAGE,yBAFA,yBACA,UACA,CAEA,0DACE,WAMJ,uFACE,yBCtBJ,2CAGE,6BAFA,yBACA,UACA,CCDF,+oBAEE,yBADA,iBACA,CAGF,ypBACE,oCA4CA,6GAEE,yBADA,aACA,CCtDJ,6CACE,iBAAkB,CAClB,2BAA4B,CAC5B,4BAA6B,CAC7B,+BAAgC,CAChC,8GAAqH,CAErH,oDACE,0CAA6C,CAC7C,gCAAiC,CAGnC,qDACE,+BAAgC,CAChC,8FAA6I,CCX/I,kDAEE,yBADA,wBACA,CAGF,qCACE,gBAGF,gGAEE,yBAGF,8CAEE,mBADA,UACA,CAGF,0GAEE,mBAGF,kDACE,yBAGF,yCACE,yBAGF,8FAEE,qCAGF,6CAGE,yBAFA,yBACA,cAEA,4FAEA,uGAIE,yBAFA,qBACA,UACA,CAIJ,2IAKE,yBAFA,qBACA,aACA,CAEA,gKACE,qBAIJ,sCACE,yBAGF,kDACE,gCCpEF,wCACE,yBAGF,yCACE,yBAGF,yCACE,yBAGF,oDACE,cCfF,sCACE,WCFJ,8CAGE,mBAFA,yBACA,kFAEA,WCFF,+DAEE,yBAGE,yEACE,eAAgB,CAChB,yBAA0B,CAC1B,8BAA+B,CAG/B,yCADA,mBAEA,4FAEA,iFACE,wBACA,kDAGF,8KAEE,uBACA,gDAKF,+IACE,YAAa,CACb,yBAA0B,CAC1B,8BAA+B,CAKjC,uFACE,YAAa,CACb,yBAA0B,CAC1B,0BAA2B,CAI3B,2GACE,YAAa,CACb,yBAA0B,CAC1B,0BAA2B,CAM/B,uHACE,eAAgB,CAChB,yBAA0B,CAC1B,0BAA2B,CCrDjC,qGAGE,yBAFA,kBACA,WAEA,wEAEA,oOAEE,yBAKF,0IAEE,UAQA,oHAEE,gBAMJ,2EACE,WAGF,oEACE,cAGF,wEACE,WAMF,mDAGE,yBAFA,kBACA,UACA,CAEA,yDAEE,yBADA,UACA,CAOF,uEACE,yBAIJ,uFAEE,yBADA,aACA,CAIJ,8DAEE,yBADA,aACA,CC3EF,oCACE,yBAGF,oCACE,yBAIA,2DACE,sCAGF,2DACE,yBCVN,8BAEE,yBADA,2CACA,CAEA,wDACE,qBAEA,sEACE,yBAGF,6HAEE,yBAEA,yJACE,sBAOF,yDAEE,oCADA,mCACA,CAFF,uDAEE,qCADA,oCACA,CAFF,yDAEE,oCADA,mCACA,CAFF,4DAEE,qCADA,oCACA,CAFF,uDAEE,qCADA,oCACA,CAFF,oDAEE,qCADA,oCACA,CAFF,qDAEE,oCADA,oCACA,CC9BR,sCACE,gCAEA,iDAGE,yBAFA,yBACA,UACA,CAIA,kDACE,cAGF,kDAEE,uCADA,wBACA,CAEA,6EACE,2BADF,oEACE,2BADF,+DACE,2BAGF,gHAEE,yBAKN,kDACE,mBAGF,4CACE,WAGF,wCACE,gCACA,cAEA,8CACE,oCCtCJ,4CAGE,8DADA,YADA,WAEA,CCNF,2MDGA,4CAMI,iEACA,4BAGF,oDAGE,iFADA,YADA,WAEA,CChBJ,2MDaE,oDAMI,iFACA,4BEnBR,8CAEE,yBADA,iBACA,CAGE,iEACE,gCAGF,iEACE,+BAGF,mEACE,8BChBN,mCACE,cAEA,qCACE,WCFJ,wCAGE,yBAFA,gCACA,UACA,CAGF,qCACE,mCACA,2BAEA,2CACE,yBACA,cCZJ,yCACE,WAEA,+CACE,cAGF,mDACE,cAEA,yDACE,cCXN,wCACE,kBAAmB,CACnB,oBAAqB,CAGrB,yBADA,wBACA,CAEA,4CACE,wCACA,4CAGF,+CACE,cAGF,8CAEE,yBADA,wBACA,CAGF,uDAEE,yBADA,yBACA,CAMA,sGACE,yBAKF,sDACE,uCACA,6CAGF,mFACE,yBAEA,iGACE,yBAIJ,8DACE,cAOF,wIACE,yBAKF,oDACE,wCACA,4CAGF,iFACE,yBAEA,+FACE,yBAIJ,4DACE,cAOF,oIACE,yBAKF,uDACE,wCACA,4CAGF,oFACE,yBAEA,kGACE,yBAIJ,+DACE,WAOF,0IACE,yBC9GN,0BAEE,yBADA,wBACA,CAEA,kCAEE,yBADA,aACA,CAGF,6BAEE,yBADA,wBACA,CAKI,mFAEE,yBADA,aACA,CAMR,gCAEE,yBADA,aACA,CCzBF,gFAEE,yBADA,aACA,CAGF,4HACE,yBAGF,4FACE,4BAEA,gGAEE,yBADA,UACA,CAKJ,kFAEE,oCADA,iBACA,CAGE,wHAOE,sCAHA,YAHA,WAKA,WAHA,SADA,kBAMA,oEAHA,OAGA,CAIA,oIACE,OACA,WAQR,2BACE,WAEA,kCACE,gCACA,WCrDF,mDACE,iCACA,cAEA,0DACE,8BACA,WCLN,kDACE,cAGF,6CACE,WAGF,gDACE,6BAGF,+DAGE,yBAFA,qBACA,aACA,CAIA,kPAGE,sCAOF,sSAEE,WAIJ,yDACE,yBAGF,yDACE,yBAGF,mGAEE,yBChDJ,sCAGE,mBAFA,qBACA,wFAEA,YAEA,6CACE,cAGF,4CAEE,yBADA,oBACA,CCXF,+CAEE,yBADA,wBACA,CAEA,wDACE,4BCJF,2GAGE,mBADA,qEACA,CCJJ,uDACE,cCCF,0CAGE,yBAFA,yBACA,cAEA,uVAEA,gDAGE,yBAFA,qBACA,UACA,CCZN,iCAEE,yBADA,wBACA,CAEA,wCACE,cAGF,sCACE,WAGF,sCACE,cCZF,sCAGE,yBAFA,yBACA,UACA,CAEA,yFAGE,yBADA,oBACA,CAEA,iGACE,UCXN,2CACE,yBAEA,mGAGE,yBADA,UACA,CAEA,2GACE,UCTN,yCACE,mCAGF,yCACE,mCAGF,mDAEE,yBADA,UACA,CAEA,uDACE,UAKF,sGAEE,yBADA,UACA,CAIJ,+DACE,yBAKE,kEACE,yBAKN,6DACE,WAGF,mHAEE,yBASF,gIACE,cAEA,iEACE,WAIJ,gDACE,cAEA,kDACE,cAMA,mEACE,aCtEN,2BACE,2DCKF,yBAFA,yBACA,WAEA,4FAEA,gFAGE,yBADA,oBACA,CAGF,6CACE,qBChBF,wDAEE,yBADA,wBACA,CAEA,8DACE,qBCNN,oBACE,qBCDF,qDACE,mCCDF,yBACE,cAEA,mCACE,6CACA,cAEA,yCACE,4CAIJ,sCAEE,yBADA,mFACA,CAEA,4CACE,yBCfN,uCACE,mCAEA,iDAEE,yBADA,uBACA,CAEA,8GAGE,yBADA,aACA,CAGF,wDAEE,yBADA,UACA,CAKF,yDACE,yBAEA,8HAEE,yBAGF,gEACE,yBAEA,mFACE,WAMR,8CACE,cAIA,sEACE,cAGF,iFACE,cAIJ,qDACE,yBAEA,gEACE,cAKF,4DACE,yBAEA,oIAEE,yBAGF,mEACE,yBAEA,yFACE,WCtER,gDAGE,yBCHF,8DACE,cAGF,gEAEE,yBADA,oBACA,CAEA,0FACE,yBAEA,eADA,uEACA,CAEA,gGACE,yBAMJ,kEACE,WACA,wEAIJ,uEACE,qBAKA,qMACE,qBCtCN,6EACE,yBCKI,qFACE,qBAIA,yNAEE,WAWE,8HACE,yBA6ER,w4DACE,gCCtGN,2EACE,yBAGE,6FACE,cAIJ,4FAGE,yBAFA,yBACA,cAEA,8HAEA,sMAIE,yBAFA,qBACA,UACA,CAKN,+JAGE,yBADA,wBACA,CAEA,yMAGE,yBAFA,qBACA,UACA,CAEA,iNACE,UClCJ,6EACE,WAGF,qFAEE,mCADA,wBACA,CAGF,uFACE,yBAKF,kFAEE,yBADA,UACA,CAKE,gGACE,yBAON,yEAEE,yBADA,wBACA,CAEA,+EAGE,yBAFA,qBACA,UACA,CAMJ,sGAEE,yBADA,UACA,CAIJ,8FACE,cAGF,8EACE,yBAEA,qFACE,cAGF,mFAGE,yBAFA,yBACA,aACA,CAEA,yFAGE,yBAFA,qBACA,UACA,CAIJ,2FAEE,yBADA,wBACA,CAEA,8GACE,yBCvEN,yKAGE,qBAGF,4IAEE,yBAGF,mDAEE,yBADA,UACA,CClBJ,qCACE,mCAGE,gDACE,sIAGF,8CACE,sIAGF,uDACE,cAGF,qDACE,cAOF,uEACE,mIAGF,qEACE,mIC7BN,gCAEE,yBADA,UACA,CAEA,uCACE,yBAGF,sCACE,qBCVJ,0BACE,yBAGF,8CACE,4BCmDF,4DAEE,cAGF,gCAGE,mBAFA,yBACA,aACA,CAGF,+BACE,mBAGF,uCACE,cAGF,oCAEE,yBADA,wBACA,CAIA,mCAGE,yBAFA,yBACA,aACA,CAEA,yCAGE,yBAFA,qBACA,UACA,CAIJ,0CAEE,yBADA,UACA,CAGF,mCAGE,yBAFA,yBACA,UACA,CAEA,yCAEE,yBADA,UACA,CAKN,kCACE,yBAOF,kEACE,yBCzHF,8CAGE,yBAFA,qBACA,aACA,CCFA,4FAEE,cAEA,sGACE,WCLJ,yCAGE,mBAFA,qBACA,UACA,CAEA,mDACE,oBAGF,+CACE,qBACA,WAGF,iDAGE,yBAFA,qBACA,iFACA,CAIJ,8CACE,cAEA,mDAEE,yBADA,UACA,CAEA,uDAGE,WADA,iBADA,cAEA,CAIJ,wDACE,cAGF,wDACE,aAKF,iDACE,cAGF,gHAEE,WAMA,2FACE,oCAKF,iEAEE,yBADA,UACA,CAIA,gFACE,WAIA,wGACE,+LAOV,uCAEE,mBADA,oBACA,CAGF,4CAEE,4FADA,oBACA,CCxFA,qDAEE,yBADA,oBACA,CAIJ,2CAGE,yBAFA,qBACA,UACA,CCXJ,yCAGE,mBAFA,yBACA,iFACA,CAEA,oDACE,cCAF,6FAEE,yBADA,wBACA,CAIA,iFACE,mCAGF,kFACE,yBAQJ,+FACE,cAEA,kDACE,aAIJ,8CACE,WCjCF,4CAEE,yBADA,wBACA,CAOF,mFACE,cAEA,gDACE,WCXJ,+CACE,yBAGF,2CACE,+BAEA,iDACE,qBAIJ,2CACE,yBAOE,kHACE,qBCvBN,iJACE,cAIA,iDACE,qBACA,cAEA,+GAGE,oCADA,UACA,CCXJ,qDACE,yBAGF,uDAGE,yBAFA,yBACA,uFACA,CAEA,kEACE,cCVR,8CACE,cAGE,oEACE,WCJJ,6EACE,+BAIA,oEACE,mBAKA,6JACE,gBCZN,iEAEE,yBADA,aACA,CAEA,+IAEE,yBAIJ,4DAGE,yBAFA,qBACA,aACA,CAEA,kEACE,yBAIJ,0DACE,qBAIA,sEACE,cAIJ,kIAIE,6BAFA,YACA,mFACA,CClCE,8EACE,aAOE,6GACE,aAKN,sMAEE,UAMJ,6EAEE,sCADA,UACA,CAIJ,oIACE,UC7BE,gFACE,aAOF,4FACE,cAGF,oFACE,cAGF,qFACE,sCAMA,wMACE,WC1BR,oDAEE,yBADA,qFACA,CAEA,4DAEE,sCADA,UACA,CCJJ,gEAEE,mCADA,yBACA,CAGF,yDACE,yBAEA,gEACE,WAGF,+DACE,mCAKE,sGAGE,mCAFA,gCACA,UACA,CAEA,4GAEE,mCADA,+BACA,CAQR,4EACE,yBAGF,iFACE,kCAGF,2EACE,mCAGF,kFACE,cAGF,kFACE,WAKE,iHACE,cAKE,+QAEE,cAiBV,sLACE,WAGF,qEACE,mCAKF,sEAGE,yBAFA,qBACA,aACA,CCTA,kDACE,0BACA,gDAGF,wDACE,aAOA,4HACE,mBAEA,gIAGE,yBAFA,yBACA,WAEA,8HAGF,sKACE,qBACA,WAGF,8IAGE,yBAFA,qBACA,iFACA,CCrHV,kHAEE,yBAGF,2CACE,WCPJ,sCACE,mCCEI,sGACE,aAKF,uGACE,WCRJ,6DACE,cAGF,+DACE,qBAEA,mEACE,cCTN,8DACE,qBAIA,4DACE,cAGF,4DACE,yBAIJ,uDACE,yBAEA,yEACE,cAIJ,sDACE,yBAEA,4DACE,cAIJ,uEAEE,mCADA,iBACA,CASE,uKACE,yBAKN,yDAEE,6BADA,0BAEA,WAIA,0DAGE,yBAFA,yBACA,WAEA,gEAEA,gEACE,qBAGF,iEAEE,yBADA,oBACA,CAIJ,6DAEE,yBADA,wBACA,CAIJ,2EACE,UAGF,4DACE,yBC5EA,iEAEE,yBADA,UACA,CAGF,wEACE,yBAIJ,gIAEE,+BAGF,2FAEE,yBADA,UACA,CAIA,wEACE,WAGF,mEACE,cAGF,2DAGE,yBAFA,6BCjCJ,kBDkCI,WC1BJ,eALA,eADA,gBAEA,mBAHA,iBAKA,kBACA,8HAEA,gEAJA,kBAIA,CD8BI,kIAGE,yBADA,aACA,CAGF,kEACE,yBAMJ,gEACE,WAEA,6EACE,yBAGF,8IAEE,yBAKN,qFACE,6BAME,2LACE,yBAGF,iKACE,yBAKN,uEACE,yBErFA,gEACE,+BAGF,wDACE,yBAKF,sDACE,WAGF,oDACE,cAGF,yDACE,cAGF,oDACE,wEAGF,sDACE,wEAGF,sDACE,wEChCJ,wCACE,cAGF,sCACE,cAEA,0CACE,WCRJ,+CACE,cCCF,qFACE,WAEA,+FACE,cAIJ,yDACE,qBAGF,mDACE,qBAEA,sEACE,WAIJ,4GAEE,cAGF,6CAGE,gCAFA,0BACA,UACA,CAEA,oDAEE,qBADA,mBAEA,wFAKF,qJAGE,yBADA,oBACA,CAEA,+LAGE,yBAFA,qBACA,UACA,CAEA,uMACE,UAIJ,6JACE,WAGF,uKAEE,eADA,UACA,CC5DJ,mEACE,8BCFJ,kDACE,gCAGE,kFACE,+BAIJ,uDACE,cAIJ,yDACE,cCfF,2CAIE,aAHQ,CAKR,qDACE,YANM,CASR,+CACE,aAVM,CAaR,wFACE,yBAGF,mDAEE,yBADA,UACA,CAEA,6DACE,SArBU,CAwBZ,uDACE,UAzBU,CAgCZ,qGACE,cAMJ,8DACE,+BAAgC,CAChC,2BAA4B,CAC5B,oBAAqB,CAGvB,uDACE,+BAAgC,CAChC,2BAA4B,CAE5B,uEACE,cACA,WCnDN,kDACE,cCEA,uFACE,cAMA,+DACE,yBAEA,sEACE,cAIJ,8DACE,yBAEA,iEACE,cCxBR,6CACE,cCFJ,2CACE,kECCE,yBAGF,6CAEE,yBADA,iEACA,CAEA,gGAEE,yBAGF,gDACE,WAIA,8DACE,cAIA,0DAEE,yBADA,UACA,CAMR,kDAEE,mCADA,8BACA,CCjCF,gDACE,yBAEA,sDACE,cAIJ,mDAGE,yBAFA,qBACA,UACA,CAGF,uDACE,qBAEA,qEAGE,mCAFA,+BACA,oBACA,CAGF,0EAGE,yBAFA,+BACA,UACA,CC1BJ,kDACE,yBCCE,mGAEE,yBAKN,yCACE,cAGE,oEAEE,yBADA,oBACA,CAKN,2CAEE,yBADA,aACA,CAGF,kDAEE,yBADA,wBACA,CAEA,uDAKE,6BAJA,aACA,oBAEA,qBADA,aAEA,CAGF,yDACE,cAGF,uDACE,+BAEA,6DACE,yBAMF,wKACE,mBAMJ,+CACE,qBACA,cAEA,2GAGE,yBADA,UACA,CCjEN,iDACE,kFCCE,cAIJ,sDACE,cAEA,2DACE,WCVJ,mDACE,yBAEA,yDACE,cAIJ,yDACE,cAGF,0DACE,qBAEA,wEAGE,mCAFA,+BACA,oBACA,CAGF,6EAGE,yBAFA,+BACA,UACA,CCxBJ,kDACE,yBAOF,+GACE,cAIA,qEACE,qBCdJ,8IAEE,+BAKE,kFACE,yBAGF,4EACE,yBCZN,+CACE,cCFJ,sDACE,yBAEA,oEACE,yBAOF,2JACE,WAGF,mEACE,cCfF,mDACE,yBCKF,4FACE,cAEA,iDACE,WCRA,yEACE,yBAEA,iGACE,yBAON,yDACE,2BACA,YCfJ,8CACE,cCDF,+CACE,sBAAuB,CAGrB,0DACE,yBAGF,4DACE,cAIJ,uDACE,sBAAuB,CAGvB,yBADA,yFACA,CAGE,wEACE,yBCrBR,yGACE,yBCDF,4CACE,yBACA,cAEA,qGAEE,yBCNJ,kEACE,cAIA,qFAGE,yBAFA,qBACA,aACA,CAGF,qKAEE,WAIJ,+DAEE,mCADA,oBACA,CAEA,qEACE,cAOF,6IACE,cC7BF,+CACE,gCAIJ,wCACE,uCACA,oFACA,cCTF,6CACE,gEACA,4CAGF,4CACE,sIACA,kDAGF,uDACE,UAGF,6CAEE,6BADA,UACA,CAEA,kDACE,cAIJ,2CACE,6JAEA,iDACE,8KAIJ,sDACE,yBAGF,kDAEE,yBADA,wBACA,CAGF,wDACE,WAGF,mEACE,yBC7CF,wDACE,mCAAoC,CAEpC,WAIA,qEACE,yBAGF,gEACE,yBAGF,gEACE,WAGF,gEAEE,yBADA,aACA,CAQJ,yHACE,gCAAiC,CAGnC,oDACE,WCnCJ,oCACE,gGAEA,gDACE,2BAA4B,CAC5B,mFAAkG,CAGlG,+BAAgC,CAChC,8CAAiD,CAGjD,8BAA+B,CAC/B,uFAAsG,CAGtG,0BAA2B,CAI3B,mGAEE,4FAEA,2HACE,WAKN,8CACE,2GAA6H,CAE7H,mDAGE,0JAFA,qBACA,UACA,CAGF,wDACE,2BAA4B,CAC5B,+FAA6H,CAQ7H,iJACE,WAKN,iDACE,qBAEA,uDACE,WAIJ,+CACE,yBAGF,oDACE,cCzDF,qDACE,sBAAuB,CACvB,2BAEA,2DACE,2BAGF,8DACE,qCAEA,oEACE,qCAIJ,8DACE,2BACA,iBAAkB,CAKpB,kEACE,cClCJ,kDACE,yBAMA,kHACE,cAEA,iFACE,WAKN,kDACE,cAEA,uDACE,WAIJ,gDAGE,6BAFA,0BACA,WAEA,WAGF,gDAGE,8BAFA,0BACA,UACA,CAIA,yDAEE,yBADA,wBACA,CAEA,+DACE,qBACA,WAGF,iEAEE,yBADA,wBACA,CAIJ,yDACE,WAKF,sEACE,aAGF,4DACE,cAKF,gEACE,yBAIA,iEACE,cC3EN,sDACE,yBCDF,4CACE,cAGF,kDAEE,yBADA,oBACA,CAEA,8EACE,cCRF,sDACE,WAGF,wDACE,yBAGF,6DACE,cAGF,6DACE,kHAEA,mEACE,mHAIJ,yDACE,0GACE,CAOF,+DACE,2GACE,CASN,2DACE,mGACE,CAOF,iEACE,oGACE,CClDJ,oDACE,cAIJ,kFACE,yBCRJ,wEACE,qCAGF,uEACE,yBAIA,2FACE,yBAGF,4FACE,yBAGF,oFACE,yBChBJ,yDACE,aAKE,qEACE,UCLJ,yHACE,WAIJ,8CACE,0CAEA,uDACE,yBAGF,yDACE,WAKF,oDACE,yBAGF,sDAEE,mCADA,qFACA,CAGF,iDAEE,yBADA,aACA,CAEA,yKAGE,yBAIJ,kDACE,cAGF,0GAEE,mBAKJ,gEACE,yBAKE,sEACE,cAMJ,8DACE,yBAIA,sEAGE,yBAFA,yBACA,WAEA,0GAEA,4EACE,qBAGF,6EAEE,yBADA,oBACA,CAMR,4CACE,+BAGF,mDAGE,yBAFA,qBACA,UACA,CAEA,uDACE,kCAIJ,2CAEE,yBADA,oBACA,CAEA,iDACE,cAMA,iGAEE,cAIJ,kDACE,yBCzHJ,wDACE,cCFJ,0CAEE,yBADA,oBACA,CAEA,gDACE,qBAGF,gDACE,cAGF,uDAEE,yBADA,oBACA,CAGF,6DAEE,yBADA,UACA,CClBF,sDACE,cCDF,oDACE,yBAGF,oDACE,yBCHF,8DACE,yBAKE,yHACE,sCAGF,0FACE,yBAGF,yFACE,yBAGF,6FAEE,yBADA,UACA,CAIJ,6EACE,sBAKF,uGACE,WAIJ,oEACE,cAGF,4EAEE,yBADA,UACA,CCvCE,6EACE,yBAIJ,4SAIE,cAGF,8EACE,8BAOF,4YAIE,WAIJ,uDAEE,yBADA,UACA,CAGF,2DACE,yBAGF,qEACE,YAGF,wDACE,yBC9CF,mEACE,yBAGF,4DACE,yBCNJ,0DACE,mBAEA,yEAEE,yBADA,0CACA,CAEA,uFACE,yB1DoIF,8CACE,yBAME,2IACE,UAQN,kEACE,sC2DzJJ,6MACE,UAGF,mKACE,yBAGE,mNACE,WAMJ,uIAEE,yBADA,UACA,CAGF,2LACE,yBAIA,mJACE,cAGF,iJACE,mCAEA,yLACE,WAIJ,mJACE,yBAEA,2LACE,WAMJ,6JACE,cAGF,2KACE,yBAKN,uNACE,4BCxDA,iIACE,cCJJ,uKDOM,yBAMA,2IACE,cAEA,uJACE,WAIJ,iKACE,cAIJ,iJACE,yBAMA,0XACE,cAEA,yMACE,WAEA,iNACE,cAKN,+JACE,yBAEA,iLACE,6BAEA,qLACE,8BACA,WAEA,+LACE,yBAGF,2MACE,iBAKN,6KAEE,yBADA,aACA,CAIJ,mLAGE,yBAFA,yBACA,aACA,CAEA,+LAIE,yBAHA,qBACA,kFACA,UACA,CAKN,uJACE,mCAEA,+JACE,aAKF,wSAGE,mCADA,aACA,CAGF,+JACE,eAIJ,+IACE,yBAMJ,uIAEE,yBADA,UACA,CAGE,kVAEE,WAEA,kWACE,UAKN,kTAEE,WEhIJ,wGAGE,yBAFA,qBACA,UACA,CAEA,8GACE,qBACA,WAGF,+GAGE,yBAFA,qBACA,UACA,CAMA,yHACE,WAEA,8IAGE,mCAFA,yBACA,UACA,CC5BV,yEACE,yBAGF,oEACE,yBAGF,gDACE,WAIA,iDAEE,yBADA,aACA,CAEA,wDAEE,yBADA,UACA,CCdA,4HACE,cAGF,6HACE,cAGF,4HAEE,yBADA,UACA,CCTA,gJACE,UAIJ,kJAGE,yBAFA,yBACA,UACA,CAEA,iTAEE,yBAIJ,mIAEE,yBADA,UACA,CCvBV,oCAEE,mCADA,qFACA,CAII,4DACE,UAIJ,gDACE,sCCXJ,yEAEE,yBADA,wBACA,CAEA,qFACE,aAGF,8EACE,qBACA,cAEA,kFACE,aAGF,yKAGE,yBADA,UACA,CAEA,iLACE,UAKN,sFACE,yBAEA,6FACE,4BAIJ,uFACE,yBAEA,8FACE,4BAGF,iGACE,WC7CN,6CAEE,yBADA,aACA,CAIA,6CACE,UAEA,mDACE,mCAON,8CAEE,yBADA,wBACA,CAEA,oDACE,yBAIJ,4EACE,kBAKF,uCAEE,mBADA,wBACA,CAGF,uCACE,sBAKF,6CACE,WAGF,yCACE,yBAEA,6CAEE,yBADA,aACA,CAEA,yGAGE,yBADA,aACA,CAMR,mEAIE,yBAFA,yBACA,aACA,CCpEF,gCACE,6BAEA,sDACE,qBAIA,gDAEE,yBADA,aACA,CAEA,sDACE,yBAUJ,wGACE,cAEA,4DACE,UAUJ,kHACE,cAEA,8DACE,UCxCN,iEACE,mCAGF,8CACE,yBAGF,6CACE,iCACA,cAEA,mDACE,qBCRE,+HACE,mCAUJ,kIACE,cAGF,gEAGE,6BAFA,yBACA,UACA,CAGF,8DACE,yBAEA,oEACE,kCAKF,sEACE,WAMJ,6DACE,sBAGF,8DACE,cAIJ,gHAEE,mBCpDF,qDACE,aAKF,sDAEE,oCADA,iFACA,CAOF,8HACE,cAGF,wDACE,aCpBF,8CACE,yBAGF,4CACE,cAGF,4CACE,yBAKE,6DAEE,yBADA,UACA,CAGF,2DACE,cAGF,2DACE,yBAIA,sEACE,aAQJ,4DAEE,yBADA,UACA,CAGF,0DACE,WAOF,wDACE,cAGF,4DACE,aCxDR,gDACE,UAGF,uCAEE,mBADA,yBAEA,0GAEA,6CAEE,yBADA,wBACA,CAGF,8CAEE,mBADA,oBACA,CCfF,sDACE,qBAGF,6CAGE,yBAEA,kDACE,6BAGF,uDACE,cAEA,mEACE,+BAIJ,mDACE,yBAGF,qDACE,qBAMJ,gDACE,yBAIA,mDACE,oCCvCN,iCACE,iCACA,cAEA,uCACE,qBCNN,6CAEE,yBADA,iFACA,CAEA,oDACE,yBAOF,mGACE,cAEA,wDACE,WChBN,iCAEE,yBADA,aACA,CAEA,+EAEE,yBCLF,iIAEE,cCHJ,2CACE,8FCDF,6CACE,gGCDF,sCACE,4FCDF,wCACE,yBAEA,8CACE,sIACA,4CCLJ,yCACE,uDCEE,mCADA,+BACA,CAOF,6EACE,cAGF,kCACE,mBAIA,wCACE,mBAGF,qDACE,oDAEA,yDACE,cAIA,+DACE,WAON,qCAGE,mBAFA,yBACA,UACA,CAEA,2CAEE,mBADA,UACA,CAKN,0CAGE,mBAFA,yBACA,UACA,CAEA,gDAEE,mBADA,UACA,CAKF,+CACE,uBAIJ,8CAEE,8BADA,wBACA,CAEA,mDACE,qBAGF,oDACE,8BAIJ,gDACE,wBC/EF,4CACE,mCAIA,qDACE,WAGF,+DACE,gGAGF,+DACE,gGAGF,6DACE,gGAIJ,gDACE,WCvBF,qCACE,aAGF,qCAEE,yBADA,aACA,CAEA,6CACE,WAGF,wFAEE,yBChBJ,0GAGE,qCAGF,kCACE,cAEA,oJAIE,WAIJ,iCACE,cAEA,uCACE,cAIJ,oDACE,WAIJ,oCAGE,yBAFA,yBACA,aACA,CC5BE,8CACE,WAEA,oDACE,cAIJ,8CAEE,WADA,iBACA,CAEA,oDASE,sCAHA,YALA,WAOA,cALA,UADA,kBAEA,WACA,SAEA,UAEA,CC1BN,6CACE,yBAEA,yDACE,6BAIJ,gDAGE,yBAFA,qBACA,UACA,CAEA,sDACE,yBCZJ,kFAEE,yBAGF,+CACE,cAIA,gFAGE,mCAFA,yBACA,+IACA,CAGE,sLAIE,qCAFA,qBACA,yIACA,CAKF,oLAIE,oCAFA,qBACA,uIACA,CCzBR,8JACE,WAIA,uGACE,oCAGF,oGACE,qCAGF,kGACE,oCAKF,+DAEE,yBADA,kBACA,CCzBJ,oCACE,wCAIA,wEAEE,yBAIJ,sCACE,cAaF,kKACE,WAEA,yCAEE,4BADA,oBACA,CAEA,6CACE,cAIJ,0CAEE,6BADA,8BACA,CAEA,8CACE,cCzCJ,0EAGE,mCADA,kBACA,CAGF,wCACE,WAIJ,yDACE,cAGF,oCACE,cAIA,6DACE,yBCvBF,8CACE,gCPFJ,kIAGE,qCAGF,0CACE,cAEA,oLAIE,WAIJ,yCACE,cAEA,+CACE,cAIJ,4DACE,WAIJ,4CAGE,yBAFA,yBACA,aACA,CQ5BE,mDACE,WAEA,yDACE,cAIJ,sDAEE,WADA,iBACA,CAEA,4DASE,sCAHA,YALA,WAOA,cALA,UADA,kBAEA,WACA,SAEA,UAEA,CAIJ,kIAGE,mBRjCJ,2IAGE,qCAGF,6CACE,cAEA,gMAIE,WAIJ,4CACE,cAEA,kDACE,cAIJ,+DACE,WAIJ,+CAGE,yBAFA,yBACA,aACA,CS/BA,qCACE,mCAGF,wCACE,qBAEA,+CACE,cAIJ,6CACE,eAGF,2CACE,aAIA,yCACE,gCACA,cAEA,+CACE,6CAIJ,+CACE,cAKF,iDAEE,yBADA,oBACA,CAKE,6DACE,4EAMA,oEACE,6EAOV,6CAEE,yBADA,oBACA,CAIA,qDACE,qBAIJ,iDACE,cAEA,mDACE,qBACA,cAEA,yDACE,iCAKF,8DAEE,yBADA,oBACA,CAGF,6DACE,4ECzFJ,oFAEE,6GCCF,mCAIA,mDACE,yBACA,wEAEA,yDACE,yBAIJ,mDACE,WAKF,oEAGE,mCAFA,qBACA,uBACA,CAIJ,4CACE,WAIA,oDACE,cAEA,0DACE,cAGF,2DACE,cAIJ,iEACE,6BAMA,kDACE,cAKF,4DACE,cAMJ,+CACE,yBACA,kBAGF,8CACE,yBACA,eAEA,qDAEE,yBADA,aACA,CAGF,qDACE,cAGF,0GAEE,yBAEA,oIACE,WAGF,oIACE,yBAMJ,iFACE,yBAIA,6EACE,cAIA,oFACE,yBAIJ,8EACE,cClHR,2CAEE,yBADA,kBACA,CAGF,iDACE,cAKE,wHAEE,mCCfN,0CAEE,mCADA,kBACA,CAGF,gDACE,cAIA,6DACE,yBAMA,sHAEE,mCAMJ,0DACE,yBAEA,kMAIE,yBADA,UACA,CC9BN,wEAEE,mCAGF,kDAGE,mCAFA,yBACA,iBACA,CCVF,kDACE,qFCCE,cAGF,mDACE,cAgBA,uJACE,yBAEA,qHAEE,yBAGF,oCARF,oDASI,yBAEA,qHAEE,0BAMR,kDACE,yBAEA,oCAHF,kDAII,yBAEA,6EACE,0BAOF,6DACE,cAGF,2DACE,yBAEA,wEACE,6BAGF,oCAPF,2DAQI,8BAMA,2EACE,6BAIJ,kEACE,sFAEA,sEACE,yBAMA,oKACE,6BAOV,0DACE,iCAGF,6DACE,cCnGN,qDACE,yBAIA,4DACE,yBAGF,gEACE,yBAKF,0EACE,cAGF,uEACE,yBAGF,kEAEE,yBADA,wBACA,CAQJ,+HACE,cAGF,kJAGE,mCADA,8BACA,CAEA,gKACE,cAsBJ,uMAIE,mCADA,8BACA,CAEA,4NACE,cAGF,gQAIE,6BAHA,8BAEA,WADA,eAEA,CAIJ,sFAGE,qBAHF,6EAGE,qBAHF,0NAGE,qBAIA,mEAGE,yBAFA,yBACA,aACA,CAIA,8EAGE,yBAFA,mBACA,UACA,CAGF,8EAEE,yBADA,oBACA,CAGF,8EACE,yBC5GJ,wFAEE,yBADA,wBACA,CAIJ,uDACE,cAIA,0DACE,cAKF,qDACE,yBAGF,0EACE,cAGF,6DACE,yBAGF,qDACE,cAQJ,iPAGE,yBAIA,+DAEE,yBADA,yBAEA,0GAEA,qEAEE,yBADA,oBACA,CAIJ,uEACE,qBACA,wFAEA,mGAEE,mBACA,sIAFA,UAEA,CAMJ,mEACE,yBAIJ,oDACE,yBAIA,+EACE,yBAKF,2DACE,6BAIA,oFACE,yBAEA,oCAHF,oFAII,8BAIJ,0FACE,yBAEA,oCAHF,0FAII,8BCvGR,iDACE,yBCOA,kIACE,cAIJ,2DACE,yBAGF,0DACE,yBAEA,sEACE,cAGF,+DACE,WAIA,qEACE,mBACA,+DAGF,gFACE,mBACA,mIAKF,sEACE,mBACA,+DAGF,iFACE,mBACA,mIAKF,uEACE,mBACA,+DAGF,kFACE,mBACA,mIAMJ,8EACE,cAKF,+DACE,qBAGF,0EACE,cAKF,wEACE,yBAGF,+EACE,yBAIJ,kGAEE,mCAGF,qCCxCA,gFACE,mBACA,sIAGF,iFACE,mBACA,sIAGF,kFACE,mBACA,uICrEA,+CAEE,yBADA,wBACA,CAGF,6DACE,sCCRN,iCACE,yBAEA,2CACE,mCAIA,wEACE,yBAIJ,+CACE,yBAGF,8CACE,yBAGF,2DACE,yBAIA,mDACE,cCzBJ,4BACE,yBAEA,iCACE,cAIJ,8BACE,yBASE,qEACE,cCrBR,uBAEE,yBADA,+BACA,CAEA,6BACE", "sources": ["webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_bootstrap-theme.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_pagination.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_panel.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_page-top-tabs.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_table.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_body.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_bootstrap-select.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_form.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_bootstrap-datetimepicker-widget.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_second-table.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_wreath-levels.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_notification.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_tooltip.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_progress.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_scroll-to-end.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_type-of-trade-label.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_remove-chart-settings-container.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_plot-vertical-scroll-btn.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_list-links.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/pocket-friends/_sidebar-btn.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_daterangepicker.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_booster-block.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_diamond-info.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_tournament-upcoming-message.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_left-sidebar_and_right-sidebar.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/top-left-block/_index.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_mdl-switch.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_site-header.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_modal_register_demo.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_only_for_registered.scss", "webpack:///./sass/mixins/_retina.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_unlocked-achievements-block.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_redirect-message.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/assets-favorites/_list.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/assets-favorites/_arrow.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/assets-favorites/_item.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_select3.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_tab-wrap.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_tabs.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_right-sidebar-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_achievements-notice.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_intl-tel-input.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_layout-container.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_personal-discount-on-all-goods.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_virtual-keyboard.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_hotkey-tooltip.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_live-demo-buttons.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_in-page-top-blocks.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_drop-down-user-info.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_chbx.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_file-input-wrap.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_drop-down-div-block.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_hr.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_mCustomScrollbar.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_footer.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_chart-settings-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/trading-panel/_trading-panel.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/trading-panel/block/_common.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/trading-panel/block/_payout-expected-profit-text.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/trading-panel/_trading-panel-multiplot.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/trading-panel/modal/_expiration-inputs-list-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/trading-panel/modal/_amount-list-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/trading-panel/modal/_index.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/auxiliary-information-panels/_market-watch-panel.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_zoom-controls.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_popover.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_mrp.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_min-expiration-time-tooltip.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_need-deposit-tooltip.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/assets-block/_index.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_tab-nav-mobile-sidebar.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_expresses-how-to-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_mobile-drop-down-list.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_payment-info-block.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_balance-info-block.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_demo-balance-form.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/tutorial/v1/_index.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/drop-down/_chart-type.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/drop-down/_window-layout-switcher.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/drop-down/_right-widget-container.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/drop-down/_drawings-list.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/drop-down/_indicators-list.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/drop-down/_indicators-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/drop-down-modal/balance/_index.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_index.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/drop-down/_fast-deposit-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_react-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_confirm-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_custom-candles-colors.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_trader-information.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_clawshorns-analytics.scss", "webpack:///./sass/mixins/_btn.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_achievement-info.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_exp-info.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_crystal-info.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_pending-order.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_unlocked-achievements.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_bonus-history-info-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_settings-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_ticket-created-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_ranking-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_mt5-demo-balance-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_after-registration-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/your-safe/_your-safe-btc-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/your-safe/_your-safe-deposit-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/your-safe/_your-safe-downgrade-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/your-safe/_your-safe-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/your-safe/_your-safe-overview-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/your-safe/_your-safe-unlock-safe-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/your-safe/_your-safe-withdrawal-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/your-safe/_your-safe-statement-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_top-ranked-players-last-24h-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_buy-info-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_left-top-info-signals-confirm-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_upload-docs-confirm-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_mt5-exchange-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_account-comparison-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_demo-balance-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_promo-code-mod50-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_repeat-max-deposit-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_ai-trading-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_change-account-currency-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_hotkeys-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/_welcome-bonus-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/pocket-friends/_share-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/daily-bonus-modal/_daily-bonus-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/modals/daily-bonus-modal/_weeks-nav.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/right-sidebar-modal/tournaments-modal/_tournament-detail-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/right-sidebar-modal/tournaments-modal/_tournament-history-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/right-sidebar-modal/tournaments-modal/_common.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/right-sidebar-modal/tournaments-modal/_tournament.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/right-sidebar-modal/tournaments-modal/_stats.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/_tournaments-ranking.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/_mt5.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/express-modal/_express-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/express-modal/_added-asset-item.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/express-modal/_add-form.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/express-modal/_add-form-info.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/_right-sidebar-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/_signals.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/_sc-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/_mt5-accounts.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/_mt5-platforms.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/widgets/_signals_widget.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/widgets/_deals_widget.scss", "webpack:///./sass/mixins/_pc-version.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/indicators/edit_modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/indicators/line_styles_modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/indicators/list_modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/drawings/_modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/drawings/_dropdown.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/drawings/_panel.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/_form_controls.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_deposit-block.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_deposit-step-2.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_deposit-step-3.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_deposit-step-4.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_deposit-steps.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_deposit-filters.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_payments-block.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_support-text.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_payment-info-block-deposit.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_sum-offer-list.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_amount-chest-info.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_message-trade-and-update.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_message-repeat-max-deposit.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_message-traders-box.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_message-welcome-bonus.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/deposit/_message-monsters-promo.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_mt5.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_achievements-page.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_page-faq.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/guide-common.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_tutorial.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_forex-glossary.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_achievements-market.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_achievements-purchases.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_gems-lotto.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_mining-page.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_deposit-compare.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_user-manual.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_affiliate-program.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_new-year-lottery.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/supportv2/_index.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_achievements-history.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_achievements-rating.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_community-help.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_withdrawal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_my-safe-page.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_my-safe-page-window-1.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_my-safe-page-window-2.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_my-safe-page-plug.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_social-trading-rewards-page.scss", "webpack:///./sass/desktop.blocks/pages/_social-trading-rewards-page-mixins.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/pages/_profile.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/mt5-new/_mt5-trading-panel-modal.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/mt5-new/partials/_mt5-pp-item.scss", "webpack:///./sass/desktop.themes.blocks/dark-blue/_partials/sidebar-modal/mt5-new/partials/_mt5-modal-top.scss"], "sourcesContent": ["#reportrange,\n.daterangepicker .input-mini,\n.daterangepicker .calendar-table {\n  border-color: #44506a !important;\n}\n\n#reportrange {\n  border: 1px solid #44506a;\n  background: #162032;\n}\n\n.dropdown-menu {\n  box-shadow: 0 2px 15px rgb(0 0 0 / 50%);\n  background-color: #222739;\n}\n\n.dropdown-item {\n  border: 1px solid #393d4a;\n  color: #8ea5c0;\n  background: #1d2130;\n\n  &.active {\n    border-color: #393d4a;\n    color: #fff;\n    background-color: #252b3c;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.pagination {\n  > li > a {\n    border: none;\n    color: #7e91a7;\n    background-color: #21273f;\n    transition: color settings(common, transitionTime), border-color settings(common, transitionTime), background-color settings(common, transitionTime), box-shadow settings(common, transitionTime);\n\n    &:hover,\n    &:focus {\n      color: #fff;\n      background-color: #444858;\n    }\n  }\n\n  .active > a {\n    color: #fff;\n    background-color: #444858;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.panel {\n  background: none;\n\n  .panel-heading {\n    color: settings(dark-blue, panel, heading, color);\n    background-color: settings(dark-blue, panel, heading, bgColor);\n  }\n\n  .panel-body {\n    border-radius: 10px;\n    background-color: settings(dark-blue, panel, body, bgColor);\n  }\n\n  .panel-heading + .panel-body {\n    border-top: none;\n    border-top-left-radius: 0;\n    border-top-right-radius: 0;\n  }\n\n  .panel-header-filters {\n    &__item {\n      border: 1px solid #5c6b80;\n    }\n  }\n}\n", ".page-top-tabs {\n  --item-color-base: #7e91a7;\n  --item-color-hover: #fff;\n  --item-color-active: #fff;\n  --item-background-color-base: #20273f;\n  --item-background-color-hover: #434858;\n  --item-background-color-active: #434858;\n\n  > ul > li > a {\n    border-radius: 6px;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n$border-color: #292d4a;\n\ntable {\n  thead {\n    color: #8fa5bf;\n  }\n\n  tbody {\n    color: #fff;\n  }\n\n  tfoot {\n    color: #fff;\n  }\n}\n\n.table {\n  > tbody > tr > td,\n  > tbody > tr > th,\n  > tfoot > tr > td,\n  > tfoot > tr > th,\n  > thead > tr > td,\n  > thead > tr > th {\n    border-color: $border-color !important;\n  }\n\n  > thead > tr > th {\n    border-color: $border-color !important;\n    border-bottom: 2px solid;\n    color: #7f838c;\n  }\n\n  &--odd-even-v1 {\n    $odd: settings(dark-blue, common, list, odd, bgColor);\n    $even: settings(dark-blue, common, list, even, bgColor);\n\n    > tbody > tr {\n      &:nth-child(odd) > td {\n        background-color: $odd;\n      }\n\n      &:nth-child(even) > td {\n        background-color: $even;\n      }\n    }\n\n    > thead > tr {\n      &:nth-child(odd) > th {\n        background-color: $even;\n      }\n\n      &:nth-child(even) > th {\n        background-color: $odd;\n      }\n    }\n  }\n\n  &--hovered-v1 {\n    > tbody > tr:hover > td {\n      background-color: settings(dark-blue, common, list, active, bgColor);\n    }\n  }\n}\n\n.table .table {\n  background-color: #171a2d;\n}\n\n// Success\n.table > tbody > tr.success > td,\n.table > tbody > tr.success > th,\n.table > tbody > tr > td.success,\n.table > tbody > tr > th.success,\n.table > tfoot > tr.success > td,\n.table > tfoot > tr.success > th,\n.table > tfoot > tr > td.success,\n.table > tfoot > tr > th.success,\n.table > thead > tr.success > td,\n.table > thead > tr.success > th,\n.table > thead > tr > td.success,\n.table > thead > tr > th.success {\n  background-color: rgba(7 79 64 / 10%);\n}\n\n// Disabled\n.table > tbody > tr.disabled > td,\n.table > tbody > tr.disabled > th,\n.table > tbody > tr > td.disabled,\n.table > tbody > tr > th.disabled,\n.table > tfoot > tr.disabled > td,\n.table > tfoot > tr.disabled > th,\n.table > tfoot > tr > td.disabled,\n.table > tfoot > tr > th.disabled,\n.table > thead > tr.disabled > td,\n.table > thead > tr.disabled > th,\n.table > thead > tr > td.disabled,\n.table > thead > tr > th.disabled {\n  background-color: rgba(0 0 0 / 10%);\n}\n", "@use \"@config/default\";\n\nbody {\n  color: #fff;\n  background-color: #151726;\n\n  &.has-bg-image {\n    background-image: url(\"#{default.$imageDir}/body/dark-blue.jpg?v1\");\n  }\n}\n", "@use \"@mixins/settings\" as *;\n@use \"sass:color\";\n\n.bootstrap-select {\n  .dropdown-toggle.btn-default {\n    border: 1px solid #44506a;\n    border-radius: settings(common, borderRadius);\n    color: #7e91a7;\n    background-color: #162032;\n\n    &:focus,\n    &:hover {\n      border: 1px solid color.adjust(#44506a, $lightness: 3%);\n      border-radius: settings(common, borderRadius);\n      color: #7e91a7;\n      background-color: color.adjust(#162032, $lightness: 3%);\n    }\n  }\n\n  .dropdown-menu {\n    background-color: #162032; // фон нельзя убирать. при быстром скроле иногда виден задний фон\n\n    li {\n      > a {\n        color: settings(dark-blue, listLinks, color);\n        background-color: settings(dark-blue, listLinks, bgColor);\n      }\n\n      &:hover,\n      &.selected {\n        > a {\n          background-color: settings(dark-blue, listLinks, item-blue--hover, bgColor);\n        }\n      }\n    }\n\n    &.open {\n      box-shadow: 0 0 10px 1px rgb(0 0 0 / 20%);\n    }\n\n    &.inner {\n      background-color: #262c41;\n    }\n  }\n\n  &.open {\n    > .dropdown-toggle.btn-default {\n      &:hover,\n      &:focus {\n        border-color: color.adjust(#3099f5, $lightness: -5%);\n        color: #9397a0;\n        background-color: transparent;\n      }\n    }\n  }\n}\n\n.bs-actionsbox,\n.bs-donebutton {\n  background-color: #2d2e31;\n}\n\n.bs-searchbox {\n  background-color: #262c41;\n}\n\n.dropdown-header {\n  color: #fff;\n  background: rgb(0 154 249 / 20%);\n}\n", "@use \"@mixins/settings\" as *;\n\ninput.form-control,\ntextarea.form-control,\nselect.form-control,\ndiv.form-control {\n  border-color: #495671;\n  color: #7e91a7 !important;\n  background-color: inherit;\n\n  &:focus,\n  &:hover {\n    border-color: #7f889c;\n    box-shadow: none;\n  }\n}\n\n.input-group {\n  // overflow: hidden; // Зачем? обрезается тултип и прочие элементы\n  border: 1px solid #495671;\n  border-radius: settings(common, borderRadius);\n  background-color: #162032;\n  transition: border-color settings(common, transitionTime), color settings(common, transitionTime);\n\n  .form-control {\n    border: 0;\n\n    &:focus,\n    &:hover {\n      border: 0;\n    }\n\n    // Fix для автозаполнения, пока на webkit только повторяется\n    &:-webkit-autofill,\n    &:-webkit-autofill:hover,\n    &:-webkit-autofill:focus {\n      -webkit-text-fill-color: #fff;\n      -webkit-background-clip: text;\n      -webkit-box-shadow: 0 0 0 1000px #162032 inset !important;\n      font-family: inherit;\n      font-weight: inherit;\n      font-size: inherit;\n      transition: background-color 0s ease-in-out 0s;\n    }\n\n    &:-internal-autofill-previewed,\n    &:-internal-autofill-previewed:hover,\n    &:-internal-autofill-previewed:focus {\n      font-family: inherit;\n      font-weight: inherit;\n      font-size: inherit;\n    }\n  }\n\n  .input-group-addon {\n    border: 0;\n    color: #bfc6d6;\n    background-color: #232f44;\n  }\n\n  .has-error & {\n    background-color: #30333c;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.bootstrap-datetimepicker-widget {\n  a[data-action] {\n    color: settings(dark-blue, listLinks, color);\n    background-color: settings(dark-blue, listLinks, bgColor);\n\n    &:hover {\n      background-color: settings(dark-blue, listLinks, item-blue--hover, bgColor);\n    }\n  }\n\n  table {\n    thead tr:first-child th:hover {\n      background-color: rgb(48 153 245 / 12%);\n    }\n\n    td {\n      width: auto;\n      height: auto;\n      line-height: 1.4;\n\n      span {\n        margin: 0;\n        border-radius: inherit;\n        width: 40px;\n        height: auto;\n        line-height: 1.4;\n\n        &:hover {\n          background: none;\n        }\n      }\n\n      &.day,\n      &.hour,\n      &.minute,\n      &.second {\n        &:hover {\n          background-color: rgb(48 153 245 / 12%);\n        }\n      }\n    }\n  }\n\n  &.dropdown-menu {\n    box-shadow: settings(dark-blue, dropDownModal, boxShadow);\n    background-color: settings(dark-blue, dropDownModal, bgColor);\n\n    &.top::after,\n    &.bottom::after {\n      border-bottom-color: #30333c;\n    }\n  }\n}\n", ".second-table {\n  $borderColor: #31343a;\n\n  border-right: 1px solid $borderColor;\n\n  > tbody > tr {\n    > td {\n      border-top: 1px solid $borderColor;\n      border-left: 1px solid $borderColor;\n\n      .var {\n        color: #fff;\n      }\n    }\n\n    &:last-child {\n      > td {\n        border-bottom: 1px solid $borderColor;\n      }\n    }\n  }\n}", ".wreath-levels {\n  &__level {\n    color: #fff;\n  }\n\n  &__label {\n    color: #8fa5bf;\n  }\n}", ".notification {\n  border-color: #464a58;\n  background-color: #23283b;\n\n  .btn-danger-light {\n    border-color: #293145;\n    background-color: #293145;\n\n    &:focus,\n    &:hover {\n      border-color: #2d364d;\n      background-color: #2d364d;\n    }\n  }\n}\n", ".tooltip-content {\n  .tooltip-text {\n    border: 1px solid #2c3245;\n    box-shadow: 0 3px 10px rgb(0 0 0 / 35%);\n    color: #fff;\n    background-color: #1f2436;\n  }\n}\n", ".progress {\n  background-color: #3e455a;\n\n  &--postfix-dot .progress__bar::before {\n    box-shadow: 0 0 0 3px rgba(255 255 255 / 30%);\n    background-color: #fff;\n  }\n\n  &--postfix-line .progress__bar::before {\n    box-shadow: 0 0 0 2px rgba(255 255 255 / 30%);\n    background-color: #fff;\n  }\n}\n", ".scroll-to-end {\n  background-color: rgba(50, 77, 107, 0.72);\n\n  &:hover {\n    background-color: rgba(50, 77, 107, 0.88);\n  }\n}", "@use \"@mixins/settings\" as *;\n\n.type-of-trade-label {\n  color: #fff;\n\n  &:hover,\n  &:focus {\n    color: #fff;\n  }\n\n  &--real {\n    border-color: #ffa031;\n    box-shadow: 0 0 4px 2px rgb(189 136 64 / 22%);\n    background-color: #352c24;\n  }\n\n  &--demo {\n    border-color: #f52914;\n    box-shadow: 0 0 4px 2px rgb(245 41 20 / 30%);\n    background-color: #531b1f;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.remove-chart-setting {\n  &__settings .s-badge {\n    border: 1px solid settings(dark-blue, common, sBadge, borderColor);\n    color: settings(dark-blue, common, sBadge, color);\n    background-color: settings(dark-blue, common, sBadge, bgColor);\n  }\n\n  &__remove-icon {\n    border: 1px solid #33373c;\n    color: #fff;\n    background-color: #262c41;\n\n    &:hover {\n      color: #fff;\n    }\n  }\n}\n\n.remove-chart-setting-wrapper {\n  .bottom-indicator-info .actions a:hover {\n    background-color: settings(dark-blue, topLeftWrap, item--active, bgColor);\n  }\n}\n", ".plot-vertical-scroll-btn {\n  border: 1px solid #33363b;\n  color: #777;\n  background: rgb(35 34 39 / 60%);\n}\n", "@use \"@mixins/settings\" as *;\n\n%color-blue2 {\n  border-radius: settings(common, borderRadius);\n  background-color: settings(dark-blue, listLinks, item-blue--hover, bgColor);\n}\n\n%color-green2 {\n  background-color: settings(dark-blue, listLinks, item-green--hover, bgColor);\n}\n\n.list-checkbox,\n.list-radio {\n  .inner {\n    color: settings(dark-blue, listLinks, color);\n    background-color: settings(dark-blue, listLinks, bgColor);\n  }\n\n  &.color-blue > label {\n    [type=\"radio\"],\n    [type=\"checkbox\"] {\n      &:checked ~ .inner {\n        @extend %color-blue2;\n      }\n    }\n\n    &.active,\n    &:hover {\n      > .inner {\n        @extend %color-blue2;\n      }\n    }\n  }\n\n  &.color-green > label {\n    [type=\"radio\"],\n    [type=\"checkbox\"] {\n      &:checked ~ .inner {\n        @extend %color-green2;\n      }\n    }\n\n    &.active,\n    &:hover {\n      > .inner {\n        @extend %color-green2;\n      }\n    }\n  }\n}\n\n.list-links {\n  > li > a {\n    color: settings(dark-blue, listLinks, color);\n    background-color: settings(dark-blue, listLinks, bgColor);\n  }\n\n  &.color-blue > li {\n    &.active,\n    &:hover {\n      > a {\n        @extend %color-blue2;\n      }\n    }\n  }\n\n  &.color-green > li {\n    &.active,\n    &:hover {\n      > a {\n        @extend %color-green2;\n      }\n    }\n  }\n}\n", ".pocket-friends-sidebar-btn {\n  --color-base: #fff;\n  --border-color-base: #1a4dde;\n  --border-color-hover: #1a4dde;\n  --background-color-base: #1a4dde;\n  --background-color-hover: radial-gradient(65.63% 73.08% at 50% 100%, #1bff67 0%, rgba(27 255 103 / 0%) 100%), #1a4dde;\n\n  &--halal {\n    --background-color-base: rgb(26 77 222 / 80%);\n    --background-color-hover: #1a4dde;\n  }\n\n  &--mobile {\n    --background-color-base: #1b4dde;\n    --background-color-hover: radial-gradient(circle at 0% 50%, rgb(27 243 111 / 100%) 0%, rgb(26 77 222 / 100%) 25%, rgb(26 77 222 / 100%) 100%);\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.daterangepicker {\n  .calendar-table {\n    border: 1px solid #444956;\n    background-color: #1e2231;\n  }\n\n  td {\n    border-radius: 0;\n  }\n\n  td.disabled,\n  option.disabled {\n    background-color: #292931;\n  }\n\n  td.in-range {\n    color: #fff;\n    background: #2a3145;\n  }\n\n  th.available:hover,\n  td.available:hover {\n    background: #2a3145;\n  }\n\n  td.off.in-range {\n    background-color: #2a3145;\n  }\n\n  td.off {\n    background-color: #1e2231;\n  }\n\n  td.active,\n  td.active:hover {\n    background-color: rgb(48 153 245 / 20%);\n  }\n\n  .ranges li {\n    border: 1px solid #444956;\n    color: #9396a0;\n    background-color: #1e2231;\n    transition: color settings(common, transitionTime), background-color settings(common, transitionTime);\n\n    &.active,\n    &:hover {\n      border-color: #444956;\n      color: #fff;\n      background-color: #293145;\n    }\n  }\n\n  .input-mini,\n  .monthselect,\n  .yearselect {\n    border-color: #44506a;\n    color: #7e91a7;\n    background-color: #162032;\n\n    &.active {\n      border-color: #7f889c;\n    }\n  }\n\n  &.ltr {\n    background-color: #262c41;\n  }\n\n  &.opensleft::after {\n    border-bottom: 6px solid #292931;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.booster-block {\n  &__header {\n    background-color: settings(dark-blue, panel, heading, bgColor);\n  }\n\n  &__content {\n    background-color: settings(dark-blue, panel, body, bgColor);\n  }\n\n  .divider {\n    background-color: #31343a;\n  }\n\n  .insufficient-level {\n    color: #d81d1d;\n  }\n}\n", ".diamond-info {\n  &__count {\n    color: #fff;\n  }\n}", ".tournament-upcoming-message {\n  border: 1px solid #464a58;\n  box-shadow: 0 5px 10px rgb(0 0 0 / 10%);\n  background: #23283b;\n  opacity: 0.9;\n}\n", "@use \"@mixins/settings\" as *;\n\n.left-sidebar,\n.right-sidebar {\n  background-color: #1e2131;\n\n  li {\n    > a {\n      --color: #8ea5c0;\n      --icon-color: var(--color);\n      --background-color: transparent;\n\n      color: var(--color);\n      background-color: var(--background-color);\n      transition: background-color 0.2s, color 0.2s;\n\n      > .fa {\n        color: var(--icon-color);\n        transition: color 0.2s;\n      }\n\n      .svg-icon,\n      > svg {\n        fill: var(--icon-color);\n        transition: fill 0.2s;\n      }\n    }\n\n    &:hover:not(.open-menu):not(.active) {\n      > a {\n        --color: #fff;\n        --icon-color: var(--color);\n        --background-color: transparent;\n      }\n    }\n\n    &.active {\n      > a {\n        --color: #fff;\n        --icon-color: var(--color);\n        --background-color: #262b3d;\n      }\n\n      &.open-menu {\n        > a {\n          --color: #fff;\n          --icon-color: var(--color);\n          --background-color: #23283b;\n        }\n      }\n    }\n\n    &.open-menu:not(.active) {\n      > a {\n        --color: #8ea5c0;\n        --icon-color: var(--color);\n        --background-color: #23283b;\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.top-left-block {\n  %in {\n    border-radius: settings(common, borderRadius);\n    color: #fff;\n    background-color: #293145;\n    transition: background-color 0.3s;\n\n    &.active,\n    &:hover {\n      background-color: #364059;\n    }\n  }\n\n  .items__link {\n    &--chart-type .svg-icon,\n    &--toggle-panel .svg-icon {\n      fill: #fff;\n    }\n\n    @extend %in;\n  }\n\n  .currencies-block {\n    &__in {\n      > div,\n      > a {\n        font-weight: 700;\n      }\n\n      @extend %in;\n    }\n\n    .pair-number-wrap .pair {\n      color: #fff;\n    }\n\n    .icon .timeframe {\n      color: #9397a0;\n    }\n\n    .icon-chart-settings {\n      color: #fff;\n    }\n  }\n\n  // Иконки слева, идут вниз\n  .info-icons {\n    &__link {\n      border-radius: settings(common, borderRadius);\n      color: settings(dark-blue, topLeftWrap, item, color);\n      background-color: settings(dark-blue, topLeftWrap, item, bgColor);\n\n      &:hover {\n        color: settings(dark-blue, topLeftWrap, item--active, color);\n        background-color: settings(dark-blue, topLeftWrap, item--active, bgColor);\n      }\n    }\n  }\n\n  .current-indicators {\n    &__controls {\n      > a:hover {\n        background-color: settings(dark-blue, topLeftWrap, item--active, bgColor);\n      }\n    }\n\n    &.open .current-indicators-collapse {\n      color: #fefefe;\n      background-color: settings(dark-blue, topLeftWrap, item--active, bgColor);\n    }\n  }\n\n  .current-indicators-collapse {\n    color: #9397a1;\n    background-color: settings(dark-blue, topLeftWrap, item, bgColor);\n  }\n}\n", ".mdl-switch {\n  &__thumb {\n    background-color: #7e91a7;\n  }\n\n  &__track {\n    background-color: #264158;\n  }\n\n  &.is-checked {\n    .mdl-switch__track {\n      background-color: rgba(#6a8fb1, 0.7);\n    }\n\n    .mdl-switch__thumb {\n      background-color: #6aa2e2;\n    }\n  }\n}\n", "@use \"sass:color\";\n@use \"@mixins/settings\" as *;\n@use \"@mixins/map-get\" as *;\n@use \"@config/default\";\n\n.site-header {\n  border-bottom: 1px solid settings(dark-blue, header, borderColor);\n  background-color: settings(dark-blue, header, bgColor);\n\n  .chart-layout-options-btn {\n    border-color: #363c4d;\n\n    .square__item {\n      background-color: #8d8f96;\n    }\n\n    &:hover,\n    &.active {\n      background-color: #363c4d;\n\n      .square__item {\n        background-color: #fff;\n      }\n    }\n  }\n\n  .user-level-text {\n    @each $type, $data in default.$wreathLevelsParams {\n      &--#{$type} {\n        border: 1px solid rgba(color.adjust(map-get-deep($data, background), $lightness: 3%), 0.7);\n        background-color: rgba(map-get-deep($data, background), 0.7);\n      }\n    }\n  }\n}\n", ".modal-register-demo {\n  background-color: rgb(0 0 0 / 70%);\n\n  &__container {\n    border: 1px solid #464a58;\n    color: #fff;\n    background-color: #23283b;\n  }\n\n  &__form {\n    label {\n      color: #8ea5c0;\n    }\n\n    input {\n      border: 1px solid #454a56;\n      background-color: transparent !important;\n\n      &::placeholder {\n        color: rgb(142 165 192 / 50%);\n      }\n\n      &:hover,\n      &:focus {\n        border: 1px solid #454a56;\n      }\n    }\n  }\n\n  &__social-btn {\n    border: 0 !important;\n  }\n\n  .ruls {\n    color: #fff;\n  }\n\n  a {\n    border-bottom: 1px solid #8ea5c0;\n    color: #8ea5c0;\n\n    &:hover {\n      border-bottom: 1px solid transparent;\n    }\n  }\n}\n", "@use \"@config/default\";\n@use \"@mixins/retina\" as *;\n\n.only-for-registered {\n  &__logo {\n    width: 218px;\n    height: 30px;\n    background: url(\"#{default.$imageDir}/logo.png\") no-repeat 0 0;\n\n    @include retina {\n      background: url(\"#{default.$imageDir}/logo_2x.png\") no-repeat 0 0;\n      background-size: 218px 30px;\n    }\n\n    &_potrade {\n      width: 106px;\n      height: 30px;\n      background: url(\"#{default.$svgDir}/icons/logo/po-trade-logo-w.svg\") no-repeat 0 0;\n\n      @include retina {\n        background: url(\"#{default.$svgDir}/icons/logo/po-trade-logo-w.svg\") no-repeat 0 0;\n        background-size: 106px 30px;\n      }\n    }\n  }\n}\n", "@mixin retina {\n  @media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (-o-min-device-pixel-ratio: 3/2), only screen and (min--moz-device-pixel-ratio: 1.5), only screen and (min-device-pixel-ratio: 1.5) {\n    @content;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.unlocked-achievements-block {\n  border-radius: settings(common, borderRadius);\n  background-color: #191f34;\n\n  .info-block {\n    &__title {\n      border-bottom: 1px solid #31343a;\n    }\n\n    &__bonus {\n      border-right: 1px solid #31343a;\n    }\n\n    &__divider {\n      border-left: 1px solid #31343a;\n    }\n  }\n}\n", ".redirect-message {\n  color: #7f838c;\n\n  > b {\n    color: #fff;\n  }\n}", "@use \"@mixins/settings\" as *;\n\n.assets-favorites-list {\n  border-bottom: 1px solid #464a58;\n  color: #fff;\n  background-color: #1b1f2f;\n}\n\n.fav-panel-switcher {\n  border: 1px solid rgb(52 62 82 / 50%);\n  color: rgb(142 165 192 / 50%);\n\n  &:hover {\n    border: 1px solid rgb(52 62 82 / 100%);\n    color: rgb(142 165 192 / 100%);\n  }\n}\n", "@use \"sass:color\";\n\n.assets-favorites-arrow {\n  color: #fff;\n\n  &:hover {\n    color: #d0d0d0;\n  }\n\n  &--disabled {\n    color: #47474e;\n\n    &:hover {\n      color: color.adjust(#47474e, $lightness: -5%);\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.assets-favorites-item {\n  --fill-opacity: 12%;\n  --stroke-opacity: 60%;\n\n  border: 1px solid #5a687e;\n  background-color: #252b3c;\n\n  svg {\n    fill: rgb(0 153 250 / var(--fill-opacity));\n    stroke: rgb(0 153 250 / var(--stroke-opacity));\n  }\n\n  &__close {\n    color: #8ea5c0;\n  }\n\n  &:hover {\n    border: 1px solid #5a687e;\n    background-color: #364059;\n  }\n\n  &.sortable-ghost {\n    border: 1px dashed #8895ab;\n    background-color: #465369;\n  }\n\n  &--active {\n    border: 1px solid #0099fa;\n\n    &:hover {\n      border: 1px solid #0099fa;\n    }\n  }\n\n  &--bg-green {\n    svg {\n      fill: rgb(0 153 98 / var(--fill-opacity));\n      stroke: rgb(98 208 104 / var(--stroke-opacity));\n    }\n\n    .assets-favorites-item__progress {\n      background-color: #1e222e;\n\n      .progress-bar {\n        background-color: #009962;\n      }\n    }\n\n    .profit__in {\n      color: #15fb70;\n    }\n\n    &:hover {\n      border: 1px solid #009962;\n    }\n\n    &.assets-favorites-item--active {\n      border: 1px solid #009962;\n    }\n  }\n\n  &--bg-red {\n    svg {\n      fill: rgb(219 68 53 / var(--fill-opacity));\n      stroke: rgb(219 68 53 / var(--stroke-opacity));\n    }\n\n    .assets-favorites-item__progress {\n      background-color: #1e222e;\n\n      .progress-bar {\n        background-color: #db4435;\n      }\n    }\n\n    .profit__in {\n      color: #f62b2b;\n    }\n\n    &:hover {\n      border: 1px solid #db4435;\n    }\n\n    &.assets-favorites-item--active {\n      border: 1px solid #db4435;\n    }\n  }\n\n  &--bg-yellow {\n    svg {\n      fill: rgb(255 193 7 / var(--fill-opacity));\n      stroke: rgb(255 193 7 / var(--stroke-opacity));\n    }\n\n    .assets-favorites-item__progress {\n      background-color: #1e222e;\n\n      .progress-bar {\n        background-color: #ffc107;\n      }\n    }\n\n    .profit__in {\n      color: #ff0;\n    }\n\n    &:hover {\n      border: 1px solid #ffc107;\n    }\n\n    &.assets-favorites-item--active {\n      border: 1px solid #ffc107;\n    }\n  }\n}\n", "@use \"sass:color\";\n@use \"@mixins/settings\" as *;\n\n.select3 {\n  border: 1px solid settings(dark-blue, select3, borderColor);\n  background-color: settings(dark-blue, select3, bgColor);\n\n  > p > .text {\n    color: settings(dark-blue, select3, color);\n    background-color: settings(dark-blue, select3, bgColor);\n  }\n\n  > ul {\n    border: 1px solid settings(dark-blue, select3, borderColor);\n    background-color: settings(dark-blue, select3, bgColor);\n\n    > li {\n      &.selected,\n      &:hover {\n        > a {\n          color: settings(dark-blue, select3, color);\n          background-color: color.adjust(settings(dark-blue, select3, bgColor), $lightness: 10%);\n        }\n      }\n    }\n  }\n\n  input {\n    color: settings(dark-blue, select3, color);\n    background-color: settings(dark-blue, select3, bgColor);\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.widget-slot__header .divider > ul,\n.tab-nav {\n  a {\n    color: #8ea5c0;\n    background-color: #272b3c;\n  }\n\n  li:not(.active):hover a {\n    background-color: #242839;\n  }\n\n  .active {\n    border-bottom-color: #3099f5;\n\n    a {\n      color: #fff;\n      background-color: #1e2131;\n    }\n  }\n\n  // Нижняя полоса\n  li {\n    position: relative;\n    border-bottom: 1px solid transparent;\n\n    &:not(.active) {\n      &::after {\n        content: \"\";\n        position: absolute;\n        left: 50%;\n        bottom: -1px;\n        width: 0;\n        height: 1px;\n        background-color: rgb(48 153 245 / 24%);\n        transition: width settings(common, transitionTime), left settings(common, transitionTime);\n      }\n\n      &:hover {\n        &::after {\n          left: 0;\n          width: 100%;\n        }\n      }\n    }\n  }\n}\n\n.m-tabs {\n  a {\n    color: #fff;\n\n    &.active {\n      border-bottom: 1px solid #3099f5;\n      color: #fff;\n    }\n  }\n}\n", ".tabs {\n  &__caption .sub-tabs & {\n    > li {\n      border-bottom: 1px dashed #98989c;\n      color: #98989c;\n\n      &.active {\n        border-bottom: 1px dashed #fff;\n        color: #fff;\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.right-sidebar-modal {\n  &__top-title2 {\n    color: #8fa5bf;\n  }\n\n  .title {\n    color: settings(dark-blue, rightSidebarModal, title, color);\n  }\n\n  &.bg-darken {\n    background: settings(dark-blue, rightSidebarModal, darkenBgColor);\n  }\n\n  &.signals-modal .tg-link a {\n    border-color: #8ea5c0;\n    color: #d5d6d8;\n    background-color: #293145;\n  }\n\n  &.trades-modal {\n    .closed-trades li:hover,\n    .opened-traders li:hover,\n    .opened-traders .order-graph-show:hover {\n      background-color: settings(dark-blue, rightSidebarModal, item--hover, bgColor);\n    }\n\n    .opened-traders .order-graph-show {\n      color: #fff;\n    }\n\n    .closed-trades .tooltip-content .tooltip-text p span,\n    .opened-traders .tooltip-content .tooltip-text p span {\n      color: #fff;\n    }\n  }\n\n  &.express-bets-modal {\n    background-color: settings(dark-blue, rightSidebarModal, bgColorSolid);\n  }\n\n  &.sc-modal .tab-wrap {\n    background-color: settings(dark-blue, rightSidebarModal, bgColor);\n  }\n\n  &__top-title,\n  &__top-title2 {\n    background-color: #2a3144;\n  }\n}\n", ".achievements-notice {\n  border-color: #464a58;\n  box-shadow: 0 3px 10px 0 rgba(0 0 0 / 15%);\n  background: #23283b;\n  opacity: 0.95;\n\n  &__label {\n    color: #8ea5c0;\n  }\n\n  &__link {\n    border-color: #293145;\n    background-color: #293145;\n  }\n}\n", ".intl-tel-input {\n  .country-list {\n    border: 1px solid #44506a;\n    background-color: #262c41;\n\n    .divider {\n      border-bottom-color: #464a58;\n    }\n  }\n}\n", ".layout-container {\n  .chart-item {\n    &::before,\n    &::after {\n      box-shadow: 0 0 5px 1px #393942;\n      background: #525261;\n    }\n  }\n}", ".personal-discount-on-all-goods {\n  &__info {\n    color: #eed961;\n  }\n}", "@use \"@mixins/settings\" as *;\n\n.virtual-keyboard {\n  &__input {\n    border: 1px solid #2c3245;\n    color: #8ea5c0;\n    background-color: #1f2334;\n    transition: border-color settings(common, transitionTime), background-color settings(common, transitionTime), box-shadow settings(common, transitionTime), color settings(common, transitionTime);\n\n    &:hover {\n      border-color: #009af9;\n      color: #fff;\n      background-color: #314463;\n    }\n  }\n}\n", ".hotkey-tooltip {\n  border: 1px solid #444956;\n  background-color: #262c41;\n\n  &__close {\n    color: #8ea5c0;\n  }\n\n  .key {\n    color: #fff;\n  }\n\n  .var {\n    color: #8ea5c0;\n  }\n}", ".live-demo-buttons {\n  a {\n    border: 1px solid #454a56;\n    color: #fff;\n    background-color: #1d2130;\n\n    &:hover,\n    &.active {\n      border-color: #009af9;\n      background-color: #314463;\n\n      svg {\n        fill: #fff;\n      }\n    }\n  }\n}\n", ".in-page-top-blocks {\n  &__link {\n    background-color: #293145;\n\n    &:hover,\n    &.active {\n      color: #fff;\n      background-color: #364059;\n\n      svg {\n        fill: #fff;\n      }\n    }\n  }\n}\n", ".drop-down-user-info {\n  &__l {\n    background-color: rgba(#2b324a, 0.9);\n  }\n\n  &__r {\n    background-color: rgba(#262c41, 0.9);\n  }\n\n  &__nav a:hover {\n    color: #fff;\n    background-color: #364059;\n\n    svg {\n      fill: #fff;\n    }\n  }\n\n  &__nav-languages.open {\n    .drop-down-user-info__nav-languages-current {\n      color: #fff;\n      background-color: #364059;\n    }\n  }\n\n  &__nav-languages-list-wrap {\n    background-color: #1e2231;\n  }\n\n  &__nav-languages-list {\n    a {\n      &:hover {\n        background-color: #293145;\n      }\n    }\n  }\n\n  .type-of-trade-label a {\n    color: #fff;\n  }\n\n  .your-achievements,\n  .real-account-stats {\n    background-color: #1f2536;\n  }\n\n  .exp-block .exp-points {\n    &__title {\n      color: #8fa5bf;\n    }\n  }\n\n  .real-account-stats p {\n    color: #8fa5bf;\n\n    span {\n      color: #fff;\n    }\n  }\n\n  .statuses {\n    color: #8fa5bf;\n\n    a {\n      color: #e6a22d;\n    }\n  }\n\n  .info {\n    &__verification-icon {\n      svg {\n        fill: #8fa5bf;\n      }\n    }\n  }\n}\n", ".chbx {\n  &__in {\n    background-color: #333b4e;\n  }\n}", "@use \"@mixins/settings\" as *;\n\n.file-input-wrap {\n  border-style: solid;\n  border-width: 2px;\n  border-color: #293145;\n  color: #fff;\n  background-color: #293145;\n  transition: color settings(common, transitionTime), background-color settings(common, transitionTime);\n\n  &:hover,\n  &:focus {\n    border-color: #3d475d;\n    background-color: #3d475d;\n  }\n\n  &--has-error {\n    border-color: #812a2d;\n  }\n}\n", ".drop-down-div-block {\n  &__no-selected-wrap {\n    border: 1px solid #495671;\n    background-color: #162032;\n\n    &:hover {\n      border-color: #7f889c;\n    }\n  }\n}", "hr {\n  border-color: #292d4a;\n}", ".mCSB_scrollTools .mCSB_draggerRail {\n  background-color: rgb(172 160 160 / 40%);\n}\n", ".footer {\n  color: #747d98;\n\n  &__nav li a {\n    border-bottom: 1px solid rgb(116 125 152 / 50%);\n    color: #747d98;\n\n    &:hover {\n      border-bottom: 1px solid rgb(116 125 152 / 0%);\n    }\n  }\n\n  .back_to_top {\n    box-shadow: 0 5px 20px rgb(0 0 0 / 25%);\n    background-color: #1f2436;\n\n    &:hover {\n      background-color: #364059;\n    }\n  }\n}\n", "@use \"sass:color\";\n\n.chart-settings-modal {\n  background-color: rgb(25 29 45 / 90%);\n\n  &__nav-link {\n    color: #838590 !important;\n    background-color: #161823;\n\n    &:focus,\n    &:hover {\n      color: #838590;\n      background-color: #161823;\n    }\n\n    &.active {\n      color: #fff;\n      background-color: #181c2b;\n    }\n  }\n\n  .types-list {\n    &__item {\n      background-color: #212738;\n\n      &:focus,\n      &:hover {\n        background-color: color.adjust(#212738, $lightness: 5%);\n      }\n\n      &.active {\n        background-color: #364059;\n\n        .types-list__label {\n          color: #fff;\n        }\n      }\n    }\n  }\n\n  &__label {\n    color: #757883;\n  }\n\n  .mdl-switch {\n    .mdl-switch__label {\n      color: #838590;\n    }\n\n    &.is-checked .mdl-switch__label {\n      color: #8ea5c0;\n    }\n  }\n\n  .change-color {\n    background-color: #151723;\n\n    &__title .fa {\n      color: #3c4557;\n    }\n  }\n\n  .interval-list {\n    &__item {\n      background-color: #212738;\n\n      &:focus,\n      &:hover {\n        background-color: color.adjust(#212738, $lightness: 5%);\n      }\n\n      &.active {\n        background-color: #364059;\n\n        .interval-list__label {\n          color: #fff;\n        }\n      }\n    }\n  }\n}\n", "@use \"sass:meta\";\n@use \"config\" as *;\n@use \"@mixins/map-get\" as *;\n\n.trading-panel {\n  .call-put-block {\n    @include meta.load-css(\"block\");\n\n    background-color: map-get-deep($trading-panel, panel-background-color);\n  }\n}\n", "@use \"@mixins/map-get\" as *;\n@use \"@mixins/settings\" as *;\n@use \"../config\" as *;\n\n.block {\n  &__title {\n    color: map-get-deep($trading-panel, title-color);\n  }\n\n  .control {\n    border-color: map-get-deep($trading-panel, border-color);\n    background-color: map-get-deep($trading-panel, item, background-color);\n\n    .control-buttons__wrapper {\n      background-color: #23283b;\n      transition: background-color 0.3s;\n      cursor: pointer;\n\n      &:hover {\n        background-color: #2c3245;\n      }\n    }\n  }\n\n  .buttons {\n    > * {\n      color: #fff;\n      transition: background-color settings(common, transitionTime);\n    }\n  }\n\n  &.opened .control {\n    border-color: #009af9;\n  }\n\n  &--expiration-inputs,\n  &--bet-amount {\n    &:not(.opened) .control:hover {\n      border-color: #343e52;\n    }\n  }\n}\n", ".payout-expected-profit-text {\n  background-color: #22273b;\n}", "@use \"@mixins/map-get\" as *;\n@use \"config\" as *;\n\nbody[class*=\"layout-\"]:not(.layout-full) {\n  .trading-panel {\n    .block {\n      .value {\n        border-color: map-get-deep($trading-panel, border-color);\n      }\n\n      .control-buttons__wrapper {\n        .fa,\n        span {\n          color: map-get-deep($trading-panel, icon-color);\n        }\n      }\n    }\n  }\n\n  .is-compact-multi {\n    .trading-panel {\n      .block {\n        &--strike-price {\n          .block {\n            &__control {\n              border: 1px solid map-get-deep($trading-panel, border-color);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n// Ниже, нижний бордер для торговой панельки\n.layout-top-1-bottom-3,\n.layout-left-2-right-1,\n.layout-horizontal-2,\n.layout-top-1-bottom-2 {\n  .layout-container .chart-item:first-child .trading-panel .call-put-block {\n    border-bottom: 1px solid map-get-deep($trading-panel, border-color);\n  }\n}\n\n.layout-top-2-bottom-1 {\n  .layout-container .chart-item {\n    &:nth-child(1),\n    &:nth-child(2) {\n      .trading-panel .call-put-block {\n        border-bottom: 1px solid map-get-deep($trading-panel, border-color);\n      }\n    }\n  }\n}\n\n.layout-left-1-right-2 {\n  .layout-container .chart-item:nth-child(2) {\n    .trading-panel .call-put-block {\n      border-bottom: 1px solid map-get-deep($trading-panel, border-color);\n    }\n  }\n}\n\n.layout-top-2-bottom-2 {\n  .layout-container .chart-item {\n    &:nth-child(1),\n    &:nth-child(2) {\n      .trading-panel .call-put-block {\n        border-bottom: 1px solid map-get-deep($trading-panel, border-color);\n      }\n    }\n  }\n}\n\n.layout-top-3-bottom-1 {\n  .layout-container .chart-item {\n    &:nth-child(1),\n    &:nth-child(2),\n    &:nth-child(3) {\n      .trading-panel .call-put-block {\n        border-bottom: 1px solid map-get-deep($trading-panel, border-color);\n      }\n    }\n  }\n}\n\n.layout-left-1-right-3 {\n  .layout-container .chart-item {\n    &:nth-child(2),\n    &:nth-child(3),\n    &:nth-child(4) {\n      .trading-panel .call-put-block {\n        border-bottom: 1px solid map-get-deep($trading-panel, border-color);\n      }\n    }\n  }\n}\n\n.layout-left-3-right-1 {\n  .layout-container .chart-item {\n    &:nth-child(1),\n    &:nth-child(2) {\n      .trading-panel .call-put-block {\n        border-bottom: 1px solid map-get-deep($trading-panel, border-color);\n      }\n    }\n  }\n}\n", ".expiration-inputs-list-modal {\n  .dops {\n    background-color: #1f2334;\n\n    .radio-wrap {\n      &__text {\n        color: #849bb7;\n      }\n    }\n\n    &__timeframes-item {\n      border: 1px solid #2c3245;\n      color: #8ea5c0;\n      background-color: #1d2130;\n      transition: background-color 0.3s, border-color 0.3s, color 0.3s;\n\n      &:hover,\n      &--active {\n        border-color: #009af9;\n        color: #fff;\n        background-color: #314463;\n      }\n    }\n  }\n\n  .btn-plus,\n  .btn-minus {\n    border: 1px solid #2c3245;\n    background-color: #1f2334;\n\n    &:hover:not(.disabled) {\n      border-color: #009af9;\n      color: #fff;\n      background-color: #314463;\n\n      svg {\n        opacity: 1;\n      }\n    }\n  }\n}\n", ".amount-list-modal {\n  .start-block {\n    input {\n      color: #fff;\n    }\n\n    .amount-field {\n      border: 1px solid #2c3245;\n      background-color: rgba(31 35 52 / 50%);\n    }\n\n    .multiply-field {\n      border: 1px solid #2c3245;\n    }\n  }\n\n  .end-block {\n    .item__inner {\n      color: #fff;\n      background-color: #314463;\n    }\n\n    .item {\n      &--active {\n        .item__inner {\n          background-color: #2e344c;\n        }\n      }\n    }\n  }\n\n  .multiply {\n    &__btn {\n      border: 1px solid #2c3245;\n      background-color: #1f2334;\n\n      &:hover {\n        border-color: #009af9;\n        color: #fff;\n        background-color: #314463;\n      }\n    }\n  }\n\n  .order-amount-limit-button-container {\n    button {\n      color: #fff;\n      background-color: #2e344c;\n    }\n  }\n\n  .order-amount-limit-settings button {\n    color: #849bb7;\n  }\n\n  .order-amount-limit {\n    background-color: #1f2334;\n\n    &__title {\n      color: #849bb7;\n    }\n\n    &__btn {\n      border: 1px solid #2c3245;\n      color: #8ea5c0;\n      background-color: #1f2334;\n\n      &:hover {\n        border-color: #009af9;\n        color: #fff;\n        background-color: #314463;\n      }\n    }\n\n    .input-group {\n      border: 1px solid #2c3245;\n      background-color: #1f2334;\n\n      .input-group-addon {\n        background-color: #2c3245;\n      }\n    }\n  }\n}\n", "@use \"sass:meta\";\n@use \"@mixins/map-get\" as *;\n@use \"../config\" as *;\n\n.drop-down-modal-wrap {\n  @include meta.load-css(\"expiration-inputs-list-modal\");\n  @include meta.load-css(\"amount-list-modal\");\n\n  .list__item--fast-bet .list__v,\n  .list__k,\n  .list__link {\n    border-color: map-get-deep($trading-panel, border-color);\n  }\n\n  .list__item--active .list__link,\n  .list__item:hover .list__link {\n    background-color: map-get-deep($trading-panel, link-hover, background-color);\n  }\n\n  .list__link {\n    color: #fff;\n    background-color: map-get-deep($trading-panel, link, background-color);\n  }\n}\n", "@use \"config\" as *;\n@use \"@mixins/map-get\" as *;\n\n.market-watch-panel {\n  background-color: map-get-deep($market-watch-panel, panel-background-color);\n\n  .pb {\n    &__start {\n      background: map-get-deep($market-watch-panel, progress-start-gradient);\n    }\n\n    &__end {\n      background: map-get-deep($market-watch-panel, progress-end-gradient);\n    }\n\n    &__number-start {\n      color: map-get-deep($market-watch-panel, progress-mark-start-color);\n    }\n\n    &__number-end {\n      color: map-get-deep($market-watch-panel, progress-mark-end-color);\n    }\n  }\n}\n\n.is-m-version.portrait .market-watch-panel {\n  .pb {\n    &__start {\n      background: map-get-deep($market-watch-panel, progress-start-gradient-mobile);\n    }\n\n    &__end {\n      background: map-get-deep($market-watch-panel, progress-end-gradient-mobile);\n    }\n  }\n}\n", "@use \"@mixins/map-get\" as *;\n@use \"@scss/desktop.themes.blocks/dark-blue/_partials/trading-panel/_config.scss\" as *;\n\n.zoom-controls {\n  color: map-get-deep($trading-panel, color);\n  background-color: map-get-deep($trading-panel, item, background-color);\n\n  select {\n    background-color: map-get-deep($trading-panel, item, background-color);\n  }\n\n  .left {\n    border-color: map-get-deep($trading-panel, border-color);\n  }\n}\n", "@use \"sass:color\";\n\n.popover {\n  background-color: #262c41;\n}\n\n.popover.bottom > .arrow::after {\n  border-bottom-color: color.adjust(#262c41, $lightness: 3%);\n}\n", "@use \"sass:color\";\n@use \"@mixins/map-get\" as *;\n\n$mrp: (\n  color: #7e91a7,\n  border-color: #44506a,\n  background-color: #162032,\n  month: (\n    selected: (\n      background-color: #222d44,\n    ),\n    start: (\n      background-color: #043758,\n    ),\n    end: (\n      background-color: #043758,\n    ),\n    hover: (\n      background-color: color.adjust(#222d44, $lightness: 1%),\n    )\n  ),\n  btn: (\n    default: (\n      border-color: #444956,\n      color: #9396a0,\n      background-color: #1e2231,\n    ),\n    default-hover: (\n      border-color: #444956,\n      color: #fff,\n      background-color: #293145,\n    ),\n    success: (\n      border-color: #025b44,\n      color: #fff,\n      background-color: #172832,\n    ),\n    success-hover: (\n      color: #fff,\n      background-color: #025b44,\n    ),\n    active: (\n      color: #fff,\n      background-color: color.adjust(#293145, $lightness: 1%),\n    ),\n  ),\n  title: (\n    color: #86a6c1,\n  ),\n  arrow: (\n    color: #465169,\n  ),\n  month-wrapper: (\n    border-color: #374863,\n    background-color: #161c2e,\n  ),\n);\n\n.mrp-yeardown,\n.mrp-yearup {\n  color: map-get-deep($mrp, arrow, color);\n}\n\n.mrp-container {\n  border: 1px solid map-get-deep($mrp, border-color);\n  color: map-get-deep($mrp, color);\n  background: map-get-deep($mrp, background-color);\n}\n\n.mrp-selected {\n  background: map-get-deep($mrp, month, selected, background-color);\n}\n\n.mrp-calendar h5 span {\n  color: map-get-deep($mrp, title, color);\n}\n\n.mrp-MonthsWrapper {\n  border: 1px solid map-get-deep($mrp, month-wrapper, border-color);\n  background-color: map-get-deep($mrp, month-wrapper, background-color);\n}\n\n.mrp-btn {\n  &--default {\n    border: 1px solid map-get-deep($mrp, btn, default, border-color);\n    color: map-get-deep($mrp, btn, default, color);\n    background-color: map-get-deep($mrp, btn, default, background-color);\n\n    &:hover {\n      border-color: map-get-deep($mrp, btn, default-hover, border-color);\n      color: map-get-deep($mrp, btn, default-hover, color);\n      background-color: map-get-deep($mrp, btn, default-hover, background-color);\n    }\n  }\n\n  &--default.active {\n    color: map-get-deep($mrp, btn, active, color);\n    background-color: map-get-deep($mrp, btn, active, background-color);\n  }\n\n  &--success {\n    border: 1px solid map-get-deep($mrp, btn, success, border-color);\n    color: map-get-deep($mrp, btn, success, color);\n    background-color: map-get-deep($mrp, btn, success, background-color);\n\n    &:hover {\n      color: map-get-deep($mrp, btn, success-hover, color);\n      background-color: map-get-deep($mrp, btn, success-hover, background-color);\n    }\n  }\n}\n\n.mrp-month:hover {\n  background-color: map-get-deep($mrp, month, hover, background-color);\n}\n\n.mrp-month-start {\n  background-color: map-get-deep($mrp, month, start, background-color);\n}\n\n.mrp-month-end {\n  background-color: map-get-deep($mrp, month, end, background-color);\n}\n", ".min-expiration-time-tooltip {\n  border-color: #575968;\n  color: #7c90a8;\n  background-color: #262c42;\n}", ".need-deposit-tooltip {\n  &__balance,\n  &__sum {\n    color: #7f93ab;\n\n    span {\n      color: #fff;\n    }\n  }\n}\n", ".assets-block {\n  &__nav-item {\n    border-color: #454a56;\n    color: #fff;\n    background: #1d2130;\n\n    .svg-icon {\n      fill: #fff !important;\n    }\n\n    &:hover {\n      border-color: #009af9;\n      color: #fff;\n    }\n\n    &--active {\n      border-color: #009af9;\n      box-shadow: 0 5px 10px rgb(0 0 0 / 10%);\n      background-color: #314463;\n    }\n  }\n\n  .schedule-desc {\n    color: #7b7f8b;\n\n    &__btn {\n      color: #fff;\n      background-color: #1d2130;\n\n      .fa {\n        margin-top: 3px;\n        margin-right: 7px;\n        color: #fff;\n      }\n    }\n\n    &__item .fa {\n      color: #8fa5bf;\n    }\n\n    .svg-icon {\n      fill: #8fa5bf;\n    }\n  }\n\n  .sort-block {\n    &__item {\n      color: #9396a0;\n    }\n\n    &__item:hover,\n    &__item--active {\n      color: #fff;\n    }\n  }\n\n  .alist {\n    &__item:not(.alist__item--no-hover) {\n      &:hover .alist__link {\n        background-color: rgba(#2b476f, 0.4);\n      }\n    }\n\n    &__item {\n      &--active .alist__link {\n        color: #fff;\n        background-color: #2b476f;\n      }\n\n      &--has-loyalty-program {\n        .alist__payout {\n          color: #fff;\n        }\n\n        &:not(.alist__item--active) {\n          .alist__link {\n            background: linear-gradient(90deg, rgb(20 70 26 / 0%) 50.15%, rgba(50 172 65 / 20%) 100%);\n          }\n        }\n      }\n    }\n  }\n\n  .search {\n    border-color: #454a56;\n    background: #1d2130;\n  }\n\n  .payout-desc {\n    border-color: #035843;\n    background: linear-gradient(269deg, rgb(50 172 65 / 0%) 50%, rgb(50 172 65 / 28%) 99.01%), #172832;\n  }\n}\n", ".tab-nav-mobile-sidebar {\n  li {\n    &.active a {\n      border-color: #009af9;\n      background-color: #314463;\n    }\n  }\n\n  a {\n    border-color: #454a56;\n    color: #fff;\n    background-color: #1d2130;\n  }\n}\n", ".expresses-how-to-modal {\n  border: 1px solid #464a58;\n  box-shadow: 0 5px 10px rgba(0 0 0 / 10%);\n  background: #23283b;\n\n  .close-btn {\n    color: #8ea5c0;\n  }\n}\n", ".mobile-drop-down-list {\n  &__title {\n    border: 1px solid #454a56;\n    background-color: #1d2130;\n  }\n\n  &__list {\n    border: 1px solid #454a56;\n    background-color: #1d2130;\n  }\n\n  &__item {\n    &:hover .mobile-drop-down-list__link {\n      background-color: rgba(#364059, 0.5);\n    }\n\n    &.active .mobile-drop-down-list__link {\n      background-color: #364059;\n    }\n  }\n\n  &__counter {\n    color: #8ea5c0;\n  }\n\n  &__icon {\n    color: #8ea5c0;\n\n    svg {\n      fill: #8ea5c0;\n    }\n  }\n\n  &__link {\n    color: #fff;\n  }\n}", ".payment-info-block {\n  &__block {\n    border: 1px solid #20273f;\n    background-color: #161a2b;\n  }\n\n  &__k {\n    color: #8fa5bf;\n  }\n\n  &__text {\n    color: #8fa5bf;\n\n    span {\n      color: #fff;\n    }\n  }\n}", "@use \"@mixins/settings\" as *;\n\n.balance-info-block {\n  &__top-in > * {\n    background-color: settings(dark-blue, header, bgColor);\n  }\n\n  &__data {\n    border-color: rgba(52 62 82 / 50%);\n\n    &:hover {\n      border-color: #343e52;\n    }\n  }\n\n  &__icon {\n    background-color: #2c3245;\n  }\n\n  &.opened {\n    border-color: #343e52;\n\n    .balance-info-block {\n      &__data {\n        border-color: #343e52;\n      }\n    }\n  }\n\n  &__label,\n  &__currency {\n    color: #8ea5c0;\n  }\n}\n", ".demo-balance-form {\n  .form-group label {\n    color: #8ea5c0;\n  }\n\n  .chips {\n    &__item {\n      border-color: #495671;\n      color: #8ea5c0;\n\n      &:hover,\n      &.active {\n        color: #fff;\n        background-color: rgba(73 86 113 / 30%);\n      }\n    }\n  }\n}\n", ".theme-dark-blue {\n  .tutorial-v1 {\n    &__body {\n      background-color: #1c202e;\n    }\n\n    .notify {\n      border: 1px solid #464a58;\n      box-shadow: 0 3px 10px 0 rgba(0 0 0 / 15%);\n      background-color: #23283b;\n\n      .btn-again {\n        color: #3797d3;\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.drop-down-modal--chart-type {\n  color: #8ea5c0;\n\n  .settings-block {\n    &__in a {\n      color: settings(dark-blue, dropDownModal, chartType, customCandlesColorsBtnColor);\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.drop-down-modal--window-layout-switcher {\n  .table > tbody > tr > td {\n    border-color: #343e52 !important;\n  }\n\n  .sq {\n    &__item {\n      background: settings(dark-blue, dropDownModal, windowLayoutSwitcher, item, bgColor);\n    }\n\n    &:hover,\n    &.active {\n      .sq__item {\n        background: settings(dark-blue, dropDownModal, windowLayoutSwitcher, item--active, bgColor);\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.drop-down-modal__choose-period {\n  .periods-list a {\n    color: settings(dark-blue, listLinks, color);\n    background-color: settings(dark-blue, listLinks, bgColor);\n\n    &.active,\n    &:hover {\n      background-color: settings(dark-blue, listLinks, item-blue--hover, bgColor);\n    }\n  }\n\n  .tg-link a {\n    border-color: #8ea5c0;\n    color: #d5d6d8;\n    background-color: #293145;\n\n    &:hover {\n      background-color: #364059;\n    }\n  }\n\n  &--wrapper {\n    border-color: #8ea5c0;\n  }\n\n  &__signal-settings {\n    .ct {\n      color: #8ea5c0;\n    }\n  }\n\n  &__choose-period,\n  &__signal-settings {\n    border: none;\n    box-shadow: 0 4px 12px rgb(0 0 0 / 25%);\n    background: #222636 !important;\n  }\n}\n", ".drop-down-modal--drawings-list {\n  .tab-body {\n    .actions-block {\n      svg {\n        fill: #8ea5c0;\n      }\n    }\n\n    .drawings-list .list-item {\n      &-block {\n        .icon-wrapper {\n          svg {\n            fill: #c5c5cb;\n          }\n        }\n      }\n\n      .drawing-icon svg,\n      &-block svg {\n        fill: #fff;\n      }\n    }\n  }\n\n  .all-block {\n    .list-item:hover {\n      color: #fff;\n      background-color: rgb(48 153 245 / 24%);\n    }\n  }\n\n  .tab-body .current-block .drawings-list .list-item-block a:hover .icon-wrapper svg {\n    fill: #fff;\n  }\n}\n", ".drop-down-modal--indicators-list {\n  .tab-body {\n    .actions-block {\n      svg {\n        fill: #8ea5c0;\n      }\n    }\n  }\n\n  .indicators-list {\n    .list-item {\n      .asset-label {\n        color: #8ea5c0;\n      }\n\n      .fav {\n        color: #ff9706;\n      }\n\n      &:hover {\n        background-color: rgba(48 153 245 / 24%);\n\n        .asset-label {\n          color: #fff;\n        }\n\n        .list-item-block {\n          color: #fff;\n        }\n      }\n    }\n  }\n}\n", ".drop-down-modal {\n  .indicators-modal {\n    box-shadow: 0 0 10px 1px rgb(0 0 0 / 20%);\n    background-color: rgb(46 46 54 / 100%);\n\n    a:hover {\n      color: #fff;\n      background-color: rgb(48 153 245 / 24%);\n    }\n  }\n}\n", "@use \"@mixins/getUiKitCssVarName\" as *;\n\n.drop-down-modal--balance {\n  .add-new-account-btn {\n    border: 1px dashed #343e52;\n    background-color: rgba(31 37 54 / 50%);\n  }\n\n  .balance-item {\n    background-color: #1f2536;\n\n    &__label {\n      color: #fff;\n    }\n\n    &:hover {\n      background-color: rgba(31 37 54 / 50%);\n    }\n\n    &--current {\n      .balance-item {\n        &__change-currency-link {\n          border-color: rgba(0 154 249 / 50%);\n          color: #fff;\n          background-color: rgba(37 43 60 / 50%);\n\n          &:hover {\n            border-color: rgba(0 154 249 / 70%);\n            background-color: rgba(37 43 60 / 70%);\n          }\n        }\n      }\n    }\n  }\n\n  .balance-item-group-wrap {\n    &__header {\n      background-color: #1f2536;\n    }\n\n    &__header-icon {\n      color: var(getUiKitCssVarName(tag, a, base));\n    }\n\n    &__items {\n      background-color: rgba(31 37 54 / 50%);\n    }\n\n    &__header-arrow {\n      color: #8ea5c0;\n    }\n\n    &__header-label {\n      color: #fff;\n    }\n\n    &--tournament {\n      .balance-item-group {\n        &__label-timer {\n          color: #8ea5c0;\n        }\n\n        &.launched {\n          .balance-item-group {\n            &__icon,\n            &__timer {\n              color: #009af9;\n            }\n          }\n        }\n      }\n    }\n\n    &--open {\n      .balance-item-group-wrap {\n        &__header-icon {\n          color: #fff;\n        }\n      }\n    }\n  }\n\n  .balance-item-group {\n    &__label {\n      color: #fff;\n    }\n\n    &:hover {\n      background-color: rgba(#364059, 30%);\n    }\n  }\n\n  .window {\n    .change-window-btn {\n      border-color: #343e52;\n      color: #8ea5c0;\n      background-color: #1d2130;\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n@use \"sass:meta\";\n\n// partials\n.theme-dark-blue {\n  @include meta.load-css(\"_partials/bootstrap-theme\");\n  @include meta.load-css(\"_partials/pagination\");\n  @include meta.load-css(\"_partials/panel\");\n  @include meta.load-css(\"_partials/page-top-tabs\");\n  @include meta.load-css(\"_partials/table\");\n  @include meta.load-css(\"_partials/body\");\n  @include meta.load-css(\"_partials/bootstrap-select\");\n  @include meta.load-css(\"_partials/form\");\n  @include meta.load-css(\"_partials/bootstrap-datetimepicker-widget\");\n  @include meta.load-css(\"_partials/second-table\");\n  @include meta.load-css(\"_partials/wreath-levels\");\n  @include meta.load-css(\"_partials/notification\");\n  @include meta.load-css(\"_partials/tooltip\");\n  @include meta.load-css(\"_partials/progress\");\n  @include meta.load-css(\"_partials/scroll-to-end\");\n  @include meta.load-css(\"_partials/type-of-trade-label\");\n  @include meta.load-css(\"_partials/remove-chart-settings-container\");\n  @include meta.load-css(\"_partials/plot-vertical-scroll-btn\");\n  @include meta.load-css(\"_partials/list-links\");\n  @include meta.load-css(\"_partials/pocket-friends\");\n  @include meta.load-css(\"_partials/daterangepicker\");\n  @include meta.load-css(\"_partials/booster-block\");\n  @include meta.load-css(\"_partials/diamond-info\");\n  @include meta.load-css(\"_partials/tournament-upcoming-message\");\n  @include meta.load-css(\"_partials/left-sidebar_and_right-sidebar\");\n  @include meta.load-css(\"_partials/top-left-block\");\n  @include meta.load-css(\"_partials/mdl-switch\");\n  @include meta.load-css(\"_partials/site-header\");\n  @include meta.load-css(\"_partials/modal_register_demo\");\n  @include meta.load-css(\"_partials/only_for_registered\");\n  @include meta.load-css(\"_partials/unlocked-achievements-block\");\n  @include meta.load-css(\"_partials/redirect-message\");\n  @include meta.load-css(\"_partials/assets-favorites/list\");\n  @include meta.load-css(\"_partials/assets-favorites/arrow\");\n  @include meta.load-css(\"_partials/assets-favorites/item\");\n  @include meta.load-css(\"_partials/select3\");\n  @include meta.load-css(\"_partials/tab-wrap\");\n  @include meta.load-css(\"_partials/tabs\");\n  @include meta.load-css(\"_partials/right-sidebar-modal\");\n  @include meta.load-css(\"_partials/achievements-notice\");\n  @include meta.load-css(\"_partials/intl-tel-input\");\n  @include meta.load-css(\"_partials/layout-container\");\n  @include meta.load-css(\"_partials/personal-discount-on-all-goods\");\n  @include meta.load-css(\"_partials/virtual-keyboard\");\n  @include meta.load-css(\"_partials/hotkey-tooltip\");\n  @include meta.load-css(\"_partials/live-demo-buttons\");\n  @include meta.load-css(\"_partials/in-page-top-blocks\");\n  @include meta.load-css(\"_partials/drop-down-user-info\");\n  @include meta.load-css(\"_partials/chbx\");\n  @include meta.load-css(\"_partials/file-input-wrap\");\n  @include meta.load-css(\"_partials/drop-down-div-block\");\n  @include meta.load-css(\"_partials/hr\");\n  @include meta.load-css(\"_partials/mCustomScrollbar\");\n  @include meta.load-css(\"_partials/footer\");\n  @include meta.load-css(\"_partials/chart-settings-modal\");\n  @include meta.load-css(\"_partials/trading-panel\");\n  @include meta.load-css(\"_partials/auxiliary-information-panels\");\n  @include meta.load-css(\"_partials/zoom-controls\");\n  @include meta.load-css(\"_partials/popover\");\n  @include meta.load-css(\"_partials/mrp\");\n  @include meta.load-css(\"_partials/min-expiration-time-tooltip\");\n  @include meta.load-css(\"_partials/need-deposit-tooltip\");\n  @include meta.load-css(\"_partials/assets-block\");\n  @include meta.load-css(\"_partials/tab-nav-mobile-sidebar\");\n  @include meta.load-css(\"_partials/expresses-how-to-modal\");\n  @include meta.load-css(\"_partials/mobile-drop-down-list\");\n  @include meta.load-css(\"_partials/payment-info-block\");\n  @include meta.load-css(\"_partials/balance-info-block\");\n  @include meta.load-css(\"_partials/demo-balance-form\");\n  @include meta.load-css(\"_partials/tutorial/v1\");\n  @include meta.load-css(\"_partials/drop-down/chart-type\");\n  @include meta.load-css(\"_partials/drop-down/window-layout-switcher\");\n  @include meta.load-css(\"_partials/drop-down/right-widget-container\");\n  @include meta.load-css(\"_partials/drop-down/drawings-list\");\n  @include meta.load-css(\"_partials/drop-down/indicators-list\");\n  @include meta.load-css(\"_partials/drop-down/indicators-modal\");\n\n  // drop-down-modal\n  @include meta.load-css(\"drop-down-modal\");\n\n  .drop-down-modal {\n    &__close-icon {\n      svg {\n        fill: rgba(#8ea5c0, 50%);\n        transition: fill 0.3s;\n      }\n\n      &:hover svg {\n        fill: #8ea5c0;\n      }\n    }\n\n    &--drawings-list,\n    &--indicators-list {\n      .tab-nav {\n        li {\n          border-bottom: none;\n\n          a {\n            border: 1px solid #454a56;\n            color: #fff;\n            background-color: #1d2130;\n            transition: color settings(common, transitionTime), background-color settings(common, transitionTime), border-color settings(common, transitionTime);\n          }\n\n          &:hover:not(.active) a {\n            border-color: #009af9;\n            color: #fff;\n          }\n\n          &.active a {\n            border-color: #009af9;\n            box-shadow: 0 5px 10px rgb(0 0 0 / 10%);\n            background-color: #314463;\n          }\n        }\n      }\n    }\n  }\n\n  @include meta.load-css(\"_partials/drop-down/fast-deposit-modal\");\n\n  // .left-sidebar-modal\n  @include meta.load-css(\"modals\");\n  @include meta.load-css(\"right-sidebar-modal\");\n  @include meta.load-css(\"_partials/sidebar-modal/tournaments-ranking\");\n  @include meta.load-css(\"_partials/sidebar-modal/mt5\");\n  @include meta.load-css(\"_partials/sidebar-modal/express-modal\");\n  @include meta.load-css(\"_partials/sidebar-modal/right-sidebar-modal\");\n  @include meta.load-css(\"_partials/sidebar-modal/signals\");\n  @include meta.load-css(\"_partials/sidebar-modal/sc-modal\");\n  @include meta.load-css(\"_partials/sidebar-modal/mt5-accounts\");\n  @include meta.load-css(\"_partials/sidebar-modal/mt5-platforms\");\n\n  .right-sidebar-modal {\n    .title2 {\n      background-color: #272b3c;\n    }\n\n    .title-position-icon {\n      &.active,\n      &:hover {\n        svg {\n          fill: #fff;\n        }\n      }\n    }\n  }\n\n  // widgets\n  .right-widget-container {\n    .widget-slots-divider hr {\n      background-color: rgb(143 165 191 / 50%);\n    }\n  }\n\n  @include meta.load-css(\"_partials/widgets/signals_widget\");\n  @include meta.load-css(\"_partials/widgets/deals_widget\");\n\n  // indicators\n  @include meta.load-css(\"_partials/indicators/edit_modal\");\n  @include meta.load-css(\"_partials/indicators/line_styles_modal\");\n  @include meta.load-css(\"_partials/indicators/list_modal\");\n\n  // drawings\n  @include meta.load-css(\"_partials/drawings/modal\");\n  @include meta.load-css(\"_partials/drawings/dropdown\");\n  @include meta.load-css(\"_partials/drawings/panel\");\n\n  // form controls\n  @include meta.load-css(\"_partials/form_controls\");\n\n  // pages\n  @include meta.load-css(\"pages\");\n}\n\n.is-mt5 {\n  @include meta.load-css(\"_partials/sidebar-modal/mt5-new\");\n}\n", ".fast-deposit-modal {\n  input[name=\"code\"],\n  input[name=\"bonus_code\"] {\n    background-color: #162032;\n  }\n\n  label {\n    color: #fff;\n  }\n}\n", ".ReactModal__Overlay {\n  background-color: rgba(#0f1013, 0.7);\n}\n", ".ReactModal__Overlay.ConfirmModalOverlay {\n  .ReactModal__Content.ConfirmModal {\n    .icon {\n      svg {\n        fill: #8ea5c0;\n      }\n    }\n\n    .content {\n      p {\n        color: #fff;\n      }\n    }\n  }\n}\n", ".custom-candles-colors {\n  .simple-dropdown {\n    svg {\n      color: #495671;\n    }\n\n    &:hover {\n      border-color: #7f889c;\n\n      svg {\n        color: #7f889c;\n      }\n    }\n  }\n}", ".trader-information-modal {\n  input.form-control {\n    color: #fff !important;\n  }\n\n  .trading-info {\n    &__k {\n      color: #8ea5c0;\n    }\n\n    li {\n      background-color: #293145;\n    }\n  }\n\n  .data-block {\n    background-color: #1d2130;\n\n    .form-block label {\n      color: #8ea5c0;\n    }\n  }\n\n  .block-msg {\n    background-color: #1d2130;\n\n    &__text {\n      color: #8ea5c0;\n    }\n  }\n\n  .copy-info-table-edit-block {\n    border-color: #ccc;\n    background-color: rgba(#ccc, 0.1);\n  }\n\n  .for-small-width {\n    .user-info {\n      &__bottom-col {\n        background-color: #1d2130;\n      }\n\n      .status-wrap {\n        background-color: #1d2130;\n      }\n    }\n  }\n\n  .copy-message {\n    border: 1px dashed #009af9;\n    background: rgb(49 68 99 / 70%);\n    opacity: 0.9;\n  }\n\n  .tbs {\n    &__nav-link {\n      border: 1px solid #454a56;\n      color: #fff;\n      background-color: #1d2130;\n      transition: border-color 0.3s;\n\n      &:hover {\n        border-color: #009af9;\n      }\n\n      &.active {\n        border-color: #009af9;\n        background-color: #314463;\n      }\n    }\n\n    &__filter-link {\n      border: 1px solid #454a56;\n      background-color: #1d2130;\n    }\n  }\n\n  .user-block .btn-wrap .svg-icon {\n    fill: #fff;\n  }\n\n  .filter-nav__btn {\n    background-color: #293145;\n  }\n}\n", "@use \"sass:color\";\n@use \"@mixins/settings\" as *;\n@use \"@mixins/btn\" as *;\n\n.clawshorns-analytics-modal {\n  .tabs__caption > li {\n    a {\n      color: #fff;\n      background-color: #2c3248;\n    }\n\n    &.active a {\n      background-color: #51566f;\n    }\n  }\n\n  .table > thead > tr > th,\n  .table > tbody > tr > td {\n    border-color: #293145 !important;\n  }\n\n  .analytics-block .item-info-block .arrow-back {\n    color: #fff;\n    background-color: #293145;\n  }\n\n  .filter {\n    .mdl-switch__label {\n      color: #fff;\n    }\n\n    .current-date {\n      color: #8fa5bf;\n    }\n\n    .btn_ {\n      border: 1px solid transparent;\n      color: #fff;\n      background-color: #293145;\n\n      @include btn;\n\n      &:hover,\n      &:focus {\n        color: settings(dark-blue, listLinks, color);\n        background-color: settings(dark-blue, listLinks, bgColor);\n      }\n\n      &.active {\n        background-color: settings(dark-blue, listLinks, item-blue--hover, bgColor);\n      }\n    }\n  }\n\n  .applications {\n    &__btn {\n      color: #fff;\n\n      &--android-apk {\n        border: 1px solid #293145;\n      }\n\n      &--ios,\n      &--android {\n        background-color: #3d4054;\n      }\n    }\n  }\n\n  .applications__item + .applications__item {\n    border-top: 1px solid #444445;\n  }\n\n  .table-calendar,\n  .table-analytics {\n    > tbody > tr {\n      &:hover > td:not(.loading) {\n        background-color: #1d2236;\n      }\n\n      &.opened > td {\n        background-color: #383d54;\n      }\n    }\n  }\n\n  .calendar-item-info-block {\n    background-color: #1f2234;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n@mixin btn() {\n  border-radius: settings(common, borderRadius);\n  padding: 7px 16px;\n  font-weight: 400;\n  font-size: 14px;\n  line-height: 1.4285;\n  white-space: normal;\n  text-align: center;\n  transition: background-color 0.3s, border-color 0.3s, color 0.3s;\n  cursor: pointer;\n  user-select: none;\n}\n", ".achievement-info-modal {\n  .item {\n    .info > div::before {\n      border-right: 1px solid #363d48;\n    }\n\n    .divider {\n      background-color: #363d48;\n    }\n  }\n\n  .type {\n    &.common {\n      color: #fff;\n    }\n\n    &.rare {\n      color: #2cccff;\n    }\n\n    &.legendary {\n      color: #2cfd7c;\n    }\n\n    &.gold {\n      box-shadow: 0 0 10px 2px #da8f1f;\n    }\n\n    &.silver {\n      box-shadow: 0 0 10px 2px #50575a;\n    }\n\n    &.bronze {\n      box-shadow: 0 0 10px 2px #863f1d;\n    }\n  }\n}", ".exp-info-modal {\n  .title {\n    color: #009af9;\n  }\n\n  .exp {\n    color: #8fa5bf;\n\n    &__in {\n      color: #fff;\n    }\n  }\n}", ".crystal-info-modal {\n  .r .title {\n    color: #009af9;\n  }\n}", "@use \"sass:color\";\n\n.pending-order-modal {\n  .bootstrap-select .dropdown-toggle.btn-default {\n    color: #fff;\n\n    .bs-caret {\n      color: #8ea5c0;\n    }\n  }\n\n  input.form-control {\n    color: #fff !important;\n  }\n\n  .input-group {\n    border-color: #2c3245;\n\n    .input-group-addon {\n      color: #fff;\n    }\n  }\n\n  .current-time,\n  .form-group > label {\n    color: #8ea5c0;\n  }\n\n  .nav > a {\n    border: 1px dashed color.adjust(#3099f5, $lightness: 5%);\n    color: #fff;\n    background: rgb(48 153 245 / 5%);\n\n    &.active {\n      border-style: solid;\n      border-color: #3099f5;\n      box-shadow: 0 0 15px rgb(48 153 245 / 50%);\n    }\n  }\n\n  .quick-timeframe-selector {\n    .btn-plus,\n    .btn-minus {\n      border-color: #2c3245;\n      background-color: #1f2334;\n\n      &:not(.disabled):hover {\n        border-color: #009af9;\n        color: #fff;\n        background-color: #314463;\n\n        svg {\n          opacity: 1;\n        }\n      }\n\n      svg {\n        opacity: 0.5;\n      }\n\n      &.disabled {\n        opacity: 0.5;\n        cursor: default;\n      }\n    }\n  }\n}\n", ".unlocked-achievements-modal {\n  .total-info {\n    &__divider {\n      border-left: 1px solid #31343a;\n    }\n  }\n}", ".bonus-history-info-modal {\n  .table {\n    border-bottom: 1px solid #34383c;\n\n    td {\n      &:first-child:not(.bonus-info) {\n        border-right: 1px solid #34383c;\n      }\n    }\n\n    span {\n      color: #8fa5bf;\n    }\n  }\n\n  .bonus-info b {\n    color: #009af9;\n  }\n}", ".settings-modal {\n  &__category {\n    $color: #8ea5c0;\n    $activeColor: #fff;\n\n    color: $color;\n\n    .svg-icon {\n      fill: $color;\n    }\n\n    .fa {\n      color: $color;\n    }\n\n    &:hover:not(.settings-modal__category--active) {\n      background-color: #293145;\n    }\n\n    &--active {\n      color: $activeColor;\n      background-color: #364059;\n\n      .svg-icon {\n        fill: $activeColor;\n      }\n\n      .fa {\n        color: $activeColor;\n      }\n    }\n  }\n\n  .new-tpl {\n    .settings-modal__content-settings {\n      .content-settings__title {\n        color: #8ea5c0;\n      }\n    }\n  }\n\n  .my-theme {\n    .select-file-block {\n      --background-color-base: #1f2334;\n      --border-color-base: #2c3245;\n      --color-base: #8ea5c0;\n    }\n\n    .file-thumb {\n      --background-color-base: #1d2232;\n      --border-color-base: #293145;\n\n      .btn-remove svg {\n        color: #8ea5c0;\n        opacity: 0.5;\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.ticket-created-modal {\n  .text > span {\n    color: settings(dark-blue, common, links, color);\n  }\n}\n", ".ranking-modal {\n  h2 {\n    color: #8fa5bf;\n  }\n\n  .trader-data {\n    .kv__k {\n      color: #8fa5bf;\n    }\n  }\n\n  .list-of-awards {\n    .awards {\n      &__title {\n        background-color: #181c2a;\n\n        .arrow {\n          color: #8fa5bf;\n        }\n      }\n\n      &__list {\n        background-color: #242838;\n\n        h3 {\n          color: #7e91a7;\n        }\n      }\n    }\n  }\n}\n", ".mt5-demo-balance-modal {\n  .fa {\n    color: #8ea5c0;\n  }\n}\n", ".after-registration-modal {\n  background-color: #1c1f30;\n}", ".your-safe-btc-modal {\n  &__r {\n    background-color: #1d2130;\n  }\n\n  &__table {\n    box-shadow: 0 0 0 1px #293145;\n    background-color: #1d2130;\n\n    th,\n    td {\n      border: 1px solid #293145;\n    }\n\n    th {\n      color: #fff;\n    }\n\n    tr {\n      td:last-child {\n        color: #8fa5bf;\n      }\n\n      &.active {\n        td {\n          color: #fff;\n          background-color: #172731;\n        }\n      }\n    }\n  }\n\n  .select-btn {\n    border-color: #20273f !important;\n    background-color: #161a2b !important;\n  }\n}\n", ".your-safe-deposit-modal {\n  &__form {\n    background-color: #141729;\n\n    label {\n      color: #7e91a7;\n    }\n  }\n\n  &__block-t {\n    border-color: #293145;\n    color: #fff;\n    background-color: #1d2232;\n  }\n\n  .input-group {\n    border-color: #20273f;\n\n    .form-control {\n      border-color: #20273f !important;\n      color: #fff !important;\n      background-color: #161a2b !important;\n    }\n\n    .input-group-addon {\n      border-right: 1px solid #20273f;\n      color: #fff;\n      background-color: #161a2b;\n    }\n  }\n}", ".your-safe-downgrade-modal {\n  &__dwnl {\n    background-color: #141729;\n  }\n}", ".your-safe-modal {\n  &__or {\n    span {\n      &::before,\n      &::after {\n        background-color: #293145;\n      }\n    }\n  }\n\n  &__terms {\n    color: #8fa5bf;\n\n    input {\n      &:checked + label::before {\n        border-color: #234cdd;\n        background-color: #234cdd;\n      }\n    }\n  }\n\n  &__message {\n    color: #8ea5c0;\n    background-color: #191d2d;\n  }\n\n  .transfer-input {\n    border: 1px solid #293145;\n    background-color: #171b2d;\n\n    &__val {\n      border: unset;\n      border-radius: unset;\n      padding: unset;\n      color: #fff !important;\n      background-color: transparent;\n    }\n\n    &__right {\n      color: #7e91a7;\n    }\n\n    &__btn {\n      border-right: 1px solid #293145;\n\n      &:hover {\n        background-color: #293145;\n      }\n    }\n\n    &.big-number-1,\n    &.big-number-2 {\n      .transfer-input__btn {\n        border-right: unset;\n      }\n    }\n  }\n\n  .chips {\n    &__item {\n      border-color: #20273f;\n      color: #74859b;\n\n      &:hover,\n      &.active {\n        color: #fff;\n        background-color: #20273f;\n      }\n    }\n  }\n}\n", ".your-safe-overview-modal {\n  &__dwnl {\n    background-color: #141729;\n  }\n}", ".your-safe-unlock-safe-modal {\n  &__text {\n    span {\n      color: #025b44;\n    }\n  }\n\n  &__r-text {\n    color: #8fa5bf;\n\n    span {\n      color: #fff;\n    }\n  }\n}", ".your-safe-withdrawal-modal {\n  &__form {\n    background-color: #141729;\n\n    label {\n      color: #7e91a7;\n    }\n  }\n\n  &__block-text {\n    color: #8fa5bf;\n  }\n\n  .input-group {\n    border-color: #20273f;\n\n    .form-control {\n      border-color: #20273f !important;\n      color: #fff !important;\n      background-color: #161a2b !important;\n    }\n\n    .input-group-addon {\n      border-right: 1px solid #20273f;\n      color: #fff;\n      background-color: #161a2b;\n    }\n  }\n}", ".your-safe-statement-modal {\n  &__form {\n    background-color: #141729;\n\n    label {\n      color: #7e91a7;\n    }\n  }\n\n  &__sub-title {\n    color: #7e91a7;\n  }\n\n  &__period-item {\n    &.active .btn {\n      border-color: #025b44;\n    }\n  }\n}", ".top-ranked-players-last-24h-modal {\n  .table > thead > tr > th,\n  .table > tbody > tr > td {\n    border-color: #293145 !important;\n  }\n\n  .pagination {\n    li {\n      &:not(.active) a {\n        background-color: #293145;\n      }\n\n      &.active a {\n        background-color: #444858;\n      }\n    }\n  }\n}\n", ".buy-info-modal {\n  .chest__title {\n    color: #ffde74;\n  }\n}\n", ".left-top-info-signals-confirm-modal {\n  background-color: #222638;\n\n  .content-wrap {\n    background-color: #1d2130;\n\n    .amount {\n      color: #fff;\n    }\n  }\n\n  .signal-progress-wrapper {\n    color: #fff;\n  }\n\n  .toggle-icon {\n    color: #8ea5c0;\n  }\n}\n", ".upload-docs-confirm-modal {\n  .block {\n    background-color: #1d2130;\n  }\n}", ".mt5-exchange-modal {\n  form {\n    label {\n      color: #8ea5c0;\n    }\n  }\n\n  .text2 {\n    color: #8ea5c0;\n\n    span {\n      color: #fff;\n    }\n  }\n}\n", ".account-comparison-modal {\n  .rw {\n    &__col {\n      &:not(.rw__col--first) {\n        background-color: #1d2130;\n\n        .rw__item:nth-child(even) {\n          background-color: #1f2539;\n        }\n      }\n    }\n  }\n\n  .tooltip2 {\n    .fa {\n      color: rgba(142 165 192 / 50%);\n      cursor: help;\n    }\n  }\n}\n", ".demo-balance-modal {\n  .or-text {\n    color: #8ea5c0;\n  }\n}\n", ".promo-code-mod50-modal {\n  .item {\n    --border-color: #464a58;\n\n    &__col {\n      &--last {\n        background-color: #262e41;\n      }\n\n      &--center {\n        color: #0099fa;\n      }\n    }\n\n    &--active {\n      --border-color: #3259a4;\n\n      box-shadow: 0 20px 40px 0 rgba(0 0 0 / 24%);\n      background-color: #293145;\n\n      .item {\n        &__col--last {\n          background-color: #1f2334;\n        }\n      }\n    }\n  }\n}\n", ".repeat-max-deposit-modal {\n  .img-wrap {\n    background-color: #1f2333;\n  }\n}\n", ".ai-trading-modal {\n  .pattern {\n    background-color: #1f2334;\n    color: #b2b2b2;\n\n    &::after,\n    &::before {\n      background-color: #1f2334;\n    }\n  }\n}\n", ".change-account-currency-modal {\n  .form-group > label {\n    color: #8ea5c0;\n  }\n\n  .exchange-block {\n    &__from-currency-block {\n      border-color: #44506a;\n      color: #7e91a7;\n      background-color: #162032;\n    }\n\n    .currency,\n    &__from-currency-block-label {\n      color: #fff;\n    }\n  }\n\n  .exchange-info {\n    border-color: #343e52;\n    background-color: rgba(31 37 54 / 50%);\n\n    &__icon {\n      color: #2c3245;\n    }\n\n    &__label {\n      color: #8ea5c0;\n    }\n\n    &__bottom {\n      color: #8ea5c0;\n    }\n  }\n}\n", ".hotkeys-modal {\n  .buttons {\n    &__line {\n      border-color: rgba(66 70 83 / 25%);\n    }\n  }\n\n  .button {\n    border: 1px solid rgba(126 145 167 / 25%);\n    box-shadow: 0 2px 0 0 rgba(1 4 10 / 28%);\n    color: #8ea5c0;\n  }\n}\n", ".welcome-bonus-modal {\n  &::before {\n    background: linear-gradient(325deg, #002167 5.24%, #009af9 78.73%);\n    filter: blur(58px);\n  }\n\n  &::after {\n    background: linear-gradient(180deg, #009af9 0%, #65c4ff 100%);\n    filter: blur(47.25px);\n  }\n\n  .modal-close svg {\n    fill: #fff;\n  }\n\n  &__timer {\n    color: #fff;\n    background: rgb(29 34 50 / 80%);\n\n    &-time {\n      color: #8ea5c0;\n    }\n  }\n\n  &__img {\n    background: linear-gradient(187deg, rgb(35 40 59 / 0%) 30.58%, #23283b 88.65%), radial-gradient(85.23% 204.8% at 50% 100%, #275672 0%, rgb(31 35 52 / 0%) 100%), #23283b;\n\n    &::after {\n      background: linear-gradient(180deg, rgb(35 40 59 / 0%) 0%, #23283b 83.68%);\n    }\n  }\n\n  .bonus__content {\n    background-color: #1d2232;\n  }\n\n  .bonus__how {\n    border: 1px solid #293145;\n    background-color: #1d2232;\n  }\n\n  .bonus__how-title {\n    color: #fff;\n  }\n\n  .bonus__steps-item--progress {\n    background-color: #2e323c;\n  }\n}\n", ".pocket-friends-share-modal {\n  .mfp-close {\n    --po-ui-kit-icon-color-base: #8ea5c0;\n\n    opacity: 0.5;\n  }\n\n  .info-block {\n    &__field-wrap {\n      border: 1px solid #44506a;\n    }\n\n    &__field {\n      background-color: #162032;\n    }\n\n    &__input {\n      color: #fff;\n    }\n\n    &__addon {\n      color: #8ea5c0;\n      background-color: #293145;\n    }\n\n    &__qr-code {\n      --background-color-hover: #293145;\n    }\n  }\n\n  .share-to {\n    --background-color-hover: #293145;\n  }\n\n  &__title {\n    color: #fff;\n  }\n}\n", ".daily-bonus-modal {\n  background: radial-gradient(circle farthest-corner at 20% -6%, rgb(73 46 133 / 100%) 0%, rgb(73 78 207 / 100%) 10%, rgb(35 40 59 / 100%) 40%);\n\n  .days-block {\n    --border-color-base: #293145;\n    --background-color-base: linear-gradient(180deg, rgb(41 49 69 / 50%) 0%, rgb(25 29 41 / 50%) 100%);\n\n    // Приз получен\n    --border-color-received: #32ac41;\n    --background-color-received: rgb(50 172 65 / 10%);\n\n    // Текущий день\n    --border-color-current: #32ac41;\n    --background-color-current: linear-gradient(180deg, rgb(50 172 65 / 60%) 0%, rgb(20 70 26 / 60%) 100%);\n\n    // Цвет title\n    --item-title-color: #8ea5c0;\n  }\n\n  .day {\n    &.received,\n    &.current {\n      box-shadow: 0 0 20px 0 rgb(50 172 65 / 25%);\n\n      .day__title {\n        color: #fff;\n      }\n    }\n  }\n\n  .last-day {\n    --background-color-base: linear-gradient(120deg, rgb(29 34 50 / 30%) 0%, rgb(29 34 50 / 30%) 60%, rgb(132 60 191 / 80%) 100%);\n\n    &__btn {\n      border-color: #8126c6;\n      color: #fff;\n      background: linear-gradient(270deg, rgb(129 38 198 / 0%) 0%, #8126c6 100%);\n    }\n\n    &--received {\n      --border-color-base: #32ac41;\n      --background-color-base: linear-gradient(120deg, rgb(29 34 50 / 30%) 0%, rgb(29 34 50 / 30%) 60%, rgb(41 129 55 / 100%) 100%);\n\n      .last-day__title {\n        color: #fff;\n      }\n    }\n\n    &--current {\n      .last-day__title {\n        color: #fff;\n      }\n    }\n  }\n\n  .block-timer {\n    border-color: #293145;\n\n    &:hover {\n      color: #fff;\n    }\n  }\n\n  .week-wrap {\n    background-color: #1d2232;\n  }\n\n  .tooltip-text p {\n    color: #8ca0da;\n  }\n}\n", "@use \"sass:color\";\n\n.daily-bonus-modal .weeks-nav {\n  // бг для всех итемов\n  $background-color: #23283b;\n  $background-color-hover: #252d3f;\n\n  // бг для текущей недели\n  $background-color-current: color.change($background-color, $alpha: 0.9); // Делаю чуть прозрачным что-бы было видно фоновую анимацию\n  $background-color-current-hover: color.change($background-color-hover, $alpha: 0.9); // Делаю чуть прозрачным что-бы было видно фоновую анимацию\n\n  &__item {\n    --border-color: #293145;\n    --background-color: #{$background-color};\n\n    &:hover {\n      --background-color: #{$background-color-hover};\n    }\n\n    &--current {\n      --background-color: #{$background-color-current};\n\n      &:hover {\n        --background-color: #{$background-color-current-hover};\n      }\n    }\n\n    &--clicked {\n      --background-color: #{$background-color-hover};\n      --text-color: #fff;\n    }\n  }\n\n  &__clicked-arrow {\n    svg {\n      color: #1d2232;\n    }\n  }\n}\n", ".tournament-detail-modal {\n  .target {\n    background-color: #1e2131;\n\n    &__title {\n      color: #8ea5c0;\n    }\n\n    &--rebuy {\n      color: #8ea5c0;\n\n      table > tr > td:first-child {\n        color: #fff;\n      }\n    }\n  }\n\n  .text11 {\n    color: #8ea5c0;\n\n    span {\n      color: #fff;\n    }\n  }\n\n  .msg1 {\n    border: 1px dashed #009af9;\n    color: #fff;\n    background: rgb(49 68 99 / 70%);\n    opacity: 0.9;\n  }\n\n  .msg2 {\n    border: 1px dashed #394a74;\n    color: #fff;\n    background: rgb(57 74 116 / 30%);\n  }\n\n  .td-tabs {\n    &__item {\n      border: 1px solid #454a56;\n      background-color: #1d2130;\n\n      &:hover {\n        border-color: #009af9;\n        color: #fff;\n      }\n\n      &--active {\n        border: 1px solid #009af9;\n        background-color: #314463;\n      }\n    }\n\n    &__link {\n      color: #fff;\n    }\n  }\n\n  .td-desc {\n    .svg-icon-wrap svg {\n      fill: #009af9;\n    }\n\n    &__info .v {\n      color: #8ea5c0;\n    }\n  }\n\n  .td-rating {\n    .active-tr {\n      background-color: #252a3b;\n    }\n\n    .text1 {\n      span {\n        color: #8ea5c0;\n      }\n    }\n  }\n}\n", ".tournament-history-modal {\n  .active-tr {\n    background-color: #252a3b;\n  }\n}\n", ".tournaments-modal {\n  .tkv__k {\n    color: #8ea5c0;\n  }\n\n  .message-info {\n    border-color: #009af9;\n    background-color: #314463;\n\n    &.notification-message::before {\n      color: #009af9;\n    }\n  }\n}\n", ".tournaments-modal {\n  .tournament {\n    &__time {\n      color: #fff;\n    }\n\n    &__footer {\n      background-color: #262b3d;\n    }\n\n    &__time-to-end {\n      color: #8ea5c0;\n    }\n\n    &--vip_bonanza {\n      background: radial-gradient(126.93% 414.63% at 98.83% 2.29%, rgb(239 198 141 / 20%) 0%, rgb(196 92 152 / 0%) 100%), #1e2131;\n\n      &:hover {\n        background: radial-gradient(126.93% 414.63% at 98.83% 2.29%, rgb(239 198 141 / 25%) 0%, rgb(196 92 152 / 0%) 100%), #1e2131;\n      }\n    }\n\n    &--day_off {\n      background:\n        radial-gradient(\n          126.93% 414.63% at 98.83% 2.29%,\n          rgb(136 51 203 / 20%) 0%,\n          rgb(0 0 0 / 0%) 100%\n        ),\n        #1e2131;\n\n      &:hover {\n        background:\n          radial-gradient(\n            126.93% 414.63% at 98.83% 2.29%,\n            rgb(136 51 203 / 25%) 0%,\n            rgb(0 0 0 / 0%) 100%\n          ),\n          #1e2131;\n      }\n    }\n\n    &--hour_play {\n      background:\n        radial-gradient(\n          131.28% 421.17% at 100% 0%,\n          rgb(8 124 199 / 20%) 0%,\n          rgb(0 0 0 / 0%) 100%\n        ),\n        #1e2131;\n\n      &:hover {\n        background:\n          radial-gradient(\n            131.28% 421.17% at 100% 0%,\n            rgb(8 124 199 / 25%) 0%,\n            rgb(0 0 0 / 0%) 100%\n          ),\n          #1e2131;\n      }\n    }\n  }\n}\n", ".tournaments-modal {\n  .stats {\n    .info {\n      &__k {\n        color: #8ea5c0;\n      }\n    }\n\n    .history table > tbody > tr:nth-child(odd) {\n      background-color: #252a3b;\n    }\n  }\n}\n", ".left-sidebar-modal.tournaments-ranking-modal {\n  .divider {\n    background-color: rgb(57 130 255 / 30%);\n  }\n\n  .title2 {\n    background-color: #272b3c;\n  }\n\n  .ranking-item {\n    &:nth-child(even) {\n      background-color: #2e354a;\n    }\n\n    &:nth-child(odd) {\n      background-color: #292f44;\n    }\n\n    &.active {\n      background-color: #3b4256;\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.left-sidebar-modal.mt5-modal {\n  .svg-icon {\n    fill: settings(dark-blue, common, links, color);\n  }\n\n  ul > li {\n    &:hover {\n      .svg-icon {\n        fill: #fff;\n      }\n\n      > a {\n        color: #fff;\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n@use \"@mixins/getUiKitVar\" as *;\n\n.express-modal {\n  .asset-item__remove {\n    a:hover {\n      color: #fff;\n    }\n  }\n\n  .express-item {\n    border-bottom: 1px solid rgb(58 57 68 / 70%);\n\n    &__wrapper {\n      background-color: #2f354e;\n    }\n\n    &.profit.win {\n      color: #fff;\n    }\n  }\n\n  .timeframe {\n    &__current {\n      border: 1px solid #44506a;\n    }\n\n    &__list-wrap {\n      box-shadow: settings(dark-blue, dropDownModal, boxShadow);\n      background-color: settings(dark-blue, dropDownModal, bgColor);\n    }\n\n    &__item {\n      color: settings(dark-blue, listLinks, color);\n      background-color: settings(dark-blue, listLinks, bgColor);\n\n      &.active,\n      &:hover,\n      &.selected {\n        background-color: settings(dark-blue, listLinks, item-blue--hover, bgColor);\n      }\n    }\n\n    &__title {\n      color: #8fa5bf;\n    }\n\n    &__current,\n    &__list-wrap {\n      background: #162032;\n    }\n  }\n\n  // ---------------------\n  .flex-vertical-block__body.lvl2 {\n    background-color: #1d2130;\n  }\n\n  .new-tab__content {\n    .asset-item {\n      &__bottom {\n        color: #8ea5c0;\n      }\n    }\n  }\n\n  .flex-vertical-block {\n    &__divider {\n      border-top-color: #363f54;\n    }\n\n    &__body-nav-list {\n      > a {\n        border: 1px solid #454a56;\n        color: #fff;\n        background-color: #1d2130;\n        transition: border-color 0.3s, background-color 0.3s;\n\n        &:hover {\n          border-color: #009af9;\n        }\n\n        &.active {\n          border-color: #009af9;\n          background-color: #314463;\n        }\n      }\n    }\n  }\n\n  .asset-item {\n    border-color: #363f54 !important;\n  }\n\n  .search-full-block {\n    border-color: #454a56;\n    color: #fff;\n    background-color: #1d2130;\n\n    .fa {\n      color: getUiKitVar(tag, a, base);\n    }\n  }\n\n  .msg-block {\n    border-color: #009af9;\n    background-color: #314463;\n\n    &__icon {\n      color: #009af9;\n    }\n  }\n\n  .ei {\n    .deals {\n      &__times,\n      &__id {\n        color: #8ea5c0;\n      }\n    }\n\n    &:nth-child(even) {\n      background-color: settings(dark-blue, rightSidebarModal, item, evenBgColor);\n    }\n  }\n}\n", ".express-modal .added-asset-item {\n  &__time {\n    color: #8ea5c0;\n  }\n}\n", ".express-modal .add-form {\n  border-color: #2c3245;\n  background-color: #1d2130;\n\n  input {\n    color: #fff !important;\n  }\n\n  label {\n    color: #8ea5c0;\n  }\n\n  .input-group {\n    border-color: #2c3244;\n    background-color: #1f2334;\n  }\n\n  .input-group-addon {\n    color: #fff;\n    background-color: #2c3244;\n  }\n}\n", ".express-modal .add-form-info {\n  &__label {\n    color: #8ea5c0;\n  }\n}\n", ".right-sidebar-modal {\n  .divider_demo {\n    background-color: #531b1f;\n  }\n\n  .divider_live {\n    background-color: #352c24;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.right-sidebar-modal.signals-modal {\n  .tab-wrap {\n    background-color: settings(dark-blue, rightSidebarModal, bgColor);\n  }\n\n  .tab-body .table {\n    tr {\n      &:hover:not(.active):not(.asset-forecast-label) > td {\n        background-color: settings(dark-blue, rightSidebarModal, item--hover, bgColor);\n      }\n\n      &:nth-child(odd) > td {\n        background-color: settings(dark-blue, common, list, odd, bgColor);\n      }\n\n      &:nth-child(even) > td {\n        background-color: settings(dark-blue, common, list, even, bgColor);\n      }\n\n      &.asset-forecast-label {\n        color: settings(dark-blue, rightSidebarModal, title, color);\n        background-color: settings(dark-blue, rightSidebarModal, title, bgColor);\n      }\n    }\n\n    .active {\n      background-color: #ffd700;\n    }\n  }\n\n  .tooltip-content .tooltip-text p {\n    &:not(:first-child) {\n      color: #fff;\n    }\n  }\n\n  .recommendation {\n    color: settings(dark-blue, rightSidebarModal, signals, recommendationColor);\n  }\n\n  .settings-block .stitle {\n    color: #fff;\n    background-color: #353d4f;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.right-sidebar-modal.sc-modal {\n  .sc-items {\n    &__item {\n      &:nth-child(even) {\n        background-color: settings(dark-blue, rightSidebarModal, item, evenBgColor);\n      }\n    }\n\n    .top-info,\n    .user-info__count-bets,\n    .user-info__pair,\n    .user-info__stop-copy {\n      color: #8ea5c0;\n    }\n\n    .ajax-loader-wrapper {\n      background: rgb(41 41 49 / 75%);\n    }\n\n    .top-info span {\n      color: #fff;\n    }\n\n    .user-info__count-bets i,\n    .user-info__pair i,\n    .user-info__stop-copy i,\n    .user-info__count-bets span {\n      color: #fff;\n    }\n  }\n\n  .stitle {\n    color: #fff;\n    background-color: #363c48;\n  }\n\n  .ssub-title {\n    background-color: #2a3144;\n  }\n\n  .tab-wrap .tab-nav li {\n    border: none;\n  }\n\n  .divider {\n    background-color: #1e2131;\n  }\n}\n", ".right-sidebar-modal.mt5-accounts-modal {\n  .tab-wrap {\n    background-color: #1b2030;\n  }\n\n  h2 {\n    background-color: #464a58;\n  }\n}\n", ".right-sidebar-modal.mt5-platforms-modal {\n  background: #2f2f35;\n\n  .platform-item {\n    border-bottom: 1px solid rgb(58 57 68 / 75%);\n    background-color: #252a3e;\n\n    &:nth-child(even) {\n      background-color: #272f43;\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.right-widget-container,\n.right-sidebar-modal {\n  .widget-slot.deals .widget-slot__header .title .list:hover svg {\n    fill: #fff;\n  }\n\n  .widget-slot.signals .widget-slot__header {\n    background-color: #2f3d4f;\n\n    .title {\n      .actions a:hover {\n        color: #fff;\n      }\n    }\n  }\n\n  .signals-list {\n    .signal-label {\n      color: #fff;\n      background-color: #1e2131;\n    }\n\n    .signal-item:not(.active):nth-child(even) {\n      background-color: settings(dark-blue, rightSidebarModal, item, evenBgColor);\n    }\n\n    .signal-item {\n      &__label {\n        color: settings(dark-blue, common, links, color);\n      }\n\n      &:hover {\n        background-color: settings(dark-blue, common, list, active, bgColor) !important;\n\n        .signal-item__label {\n          color: #fff;\n        }\n      }\n\n      &.active {\n        background-color: settings(dark-blue, common, list, active, bgColor);\n\n        .signal-item__label {\n          color: #fff;\n        }\n      }\n    }\n\n    .copy-signal-item {\n      &__stats {\n        color: #8ea5c0;\n      }\n\n      &:nth-child(even) {\n        background-color: settings(dark-blue, rightSidebarModal, item, evenBgColor);\n      }\n    }\n  }\n\n  &.signals-modal .flex-vertical-block__header .tab-nav li:not(.active) {\n    border-bottom-color: #293145;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n@use \"@mixins/pc-version\" as *;\n\n.right-sidebar-modal,\n.right-widget-container {\n  .deals-list {\n    &__group-label {\n      color: #8ea5c0;\n\n      @include pc-version {\n        background-color: #2c3042;\n      }\n    }\n\n    &__item {\n      .item-row {\n        a {\n          color: settings(dark-blue, common, links, color);\n\n          &:hover {\n            color: #fff;\n          }\n        }\n\n        .favorites a {\n          color: #ff9706;\n        }\n      }\n\n      .rollover-form {\n        background-color: #1f2334;\n\n        .rollover-form_tooltip {\n          color: #7d879d;\n        }\n\n        .rollover-info-block {\n          color: #7d879d;\n\n          &__value {\n            color: #fff;\n\n            &--up {\n              color: #6fc274;\n            }\n          }\n        }\n\n        .block {\n          border: 1px solid #535562;\n\n          .buttons {\n            border-top: 1px solid #535562;\n\n            > * {\n              border-left: 1px solid #535562;\n              color: #fff;\n\n              &:hover {\n                background-color: #1f2334;\n              }\n\n              &:first-child {\n                border-left: none;\n              }\n            }\n          }\n\n          &__title {\n            color: #7d879d;\n            background-color: #1f2334;\n          }\n        }\n\n        .value-buttons a {\n          border: 1px solid #454a56;\n          color: #8e9097;\n          background-color: #1d2130;\n\n          &:hover {\n            border-color: #009af9;\n            box-shadow: 0 5px 10px rgb(0 0 0 / 10%);\n            color: #fff;\n            background-color: #314463;\n          }\n        }\n      }\n\n      .rollover-overlay {\n        background-color: rgb(31 35 52 / 60%);\n\n        svg {\n          fill: #6fc274;\n        }\n      }\n\n      &-full {\n        .forecast,\n        .price-chart {\n          color: #8ea5c0;\n          background-color: rgb(25 29 45 / 90%);\n        }\n\n        .price-chart svg {\n          stroke: #4a76a8;\n        }\n      }\n\n      &:nth-child(even) {\n        background-color: settings(dark-blue, rightSidebarModal, item, evenBgColor);\n      }\n    }\n  }\n\n  .widget-slot__header {\n    .title {\n      color: settings(dark-blue, rightSidebarModal, title, color);\n      background-color: settings(dark-blue, rightSidebarModal, title, bgColor);\n\n      .actions {\n        a:hover,\n        .active {\n          color: #fff;\n\n          svg {\n            fill: #fff;\n          }\n        }\n      }\n\n      span,\n      a.list:hover {\n        color: settings(dark-blue, rightSidebarModal, title, color);\n      }\n    }\n  }\n}\n", "@use \"@config/default\";\n\n@mixin pc-version {\n  body.#{default.$pc-version-class-name} & {\n    @content;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.ReactModal__Overlay.EditIndicatorOverlay {\n  .ReactModal__Content.EditIndicator {\n    .m-tabs a {\n      border-color: #454a56;\n      color: #fff;\n      background-color: #1d2130;\n\n      &:hover {\n        border-color: #454a56;\n        color: #fff;\n      }\n\n      &.active {\n        border-color: #009af9;\n        color: #fff;\n        background-color: #314463;\n      }\n    }\n\n    .m-content {\n      .inputs {\n        .fields {\n          color: #fff;\n\n          .list-style-selector {\n            border: 1px solid #464a58;\n            color: #fff;\n            background-color: settings(dark-blue, dropDownModal, bgColor);\n          }\n        }\n      }\n    }\n  }\n}\n", ".line-styles-modal {\n  .line-width-list .list-body a.active {\n    background-color: #293145;\n  }\n\n  .color-list .list-body a.active {\n    border: 2px solid #444956;\n  }\n\n  .list-label {\n    color: #fff;\n  }\n\n  .list-body {\n    a {\n      color: #9396a0;\n      background-color: #1e2231;\n\n      &.active {\n        color: #fff;\n        background-color: #293145;\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.ReactModal__Overlay.IndicatorsListOverlay {\n  .ReactModal__Content.IndicatorsList {\n    .m-content {\n      .indicator {\n        .icon {\n          color: #ff9706;\n        }\n\n        .label {\n          color: #a6c6e0;\n        }\n\n        &:hover {\n          color: #fff;\n          background-color: #364059;\n        }\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.ReactModal__Overlay.DrawingsListOverlay {\n  .ReactModal__Content.DrawingsList {\n    .m-content {\n      .drawings-modal__item {\n        &-info {\n          .drawing-icon {\n            fill: #fff;\n          }\n        }\n\n        .list-style-selector {\n          border: 1px solid #464a58;\n          color: #fff;\n          background-color: #262c41;\n\n          &:hover,\n          &.active {\n            border: 1px solid #87919e;\n          }\n        }\n\n        &:hover {\n          color: #fff;\n          background-color: #364059;\n        }\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.drawings-dropdown {\n  box-shadow: settings(dark-blue, dropDownModal, boxShadow);\n  background-color: settings(dark-blue, dropDownModal, bgColor);\n\n  &__item {\n    .drawing-icon {\n      svg {\n        fill: #fff;\n      }\n    }\n\n    &:hover {\n      background-color: rgb(48 153 245 / 24%);\n    }\n  }\n}\n", "@use \"sass:color\";\n\n.drawing-settings-panel-wrapper {\n  .drawing-settings-panel {\n    border-color: transparent;\n    background-color: #40475a;\n\n    &__handle svg {\n      fill: #737376;\n    }\n\n    &__btn {\n      border-color: #575961;\n      color: #c5c5cb;\n\n      svg {\n        fill: #c5c5cb;\n      }\n\n      &:hover,\n      &.active {\n        color: #fff;\n        background-color: color.adjust(#40475a, $lightness: -2%);\n\n        svg {\n          fill: #fff;\n        }\n      }\n    }\n\n    &__colors-list {\n      background-color: #30374d;\n\n      &::before {\n        border-bottom-color: #30374d;\n      }\n    }\n\n    &__weights-list {\n      background-color: #30374d;\n\n      &::before {\n        border-bottom-color: #30374d;\n      }\n\n      a .active {\n        color: #fff;\n      }\n    }\n  }\n}\n", ".input-wrapper {\n  &__input input {\n    color: #7e91a7;\n    background-color: #162032;\n  }\n\n  &__arrows {\n    &-item {\n      fill: #fff;\n\n      &:hover {\n        background-color: rgb(38 44 65 / 30%);\n      }\n    }\n  }\n}\n\n.checkbox-wrapper {\n  .checkmark {\n    border: 1px solid #282f43;\n    background-color: #5e616d;\n\n    &:hover {\n      border: 1px solid #87919e;\n    }\n  }\n\n  .checkbox-inner-wrapper .checkmark::after {\n    border-color: #fff;\n  }\n}\n\n.range-wrapper {\n  .track {\n    border: 1px solid #282f43;\n    background: #5e616d;\n  }\n\n  .thumb {\n    background-color: #fff;\n  }\n}\n\n.dropdown-wrapper {\n  > div .icon {\n    color: #fff;\n  }\n\n  &--list {\n    background-color: #262c41;\n\n    > div {\n      color: #d5d6d8;\n      background-color: #293145;\n\n      &.selected,\n      &:hover {\n        color: #d5d6d8;\n        background-color: #364059;\n      }\n    }\n  }\n}\n\n.input-wrapper,\n.dropdown-wrapper {\n  border: 1px solid #44506a;\n  color: #7e91a7;\n  background-color: #162032;\n}\n", ".deposit-block {\n  background: rgba(19 22 40 / 90%);\n\n  .payment-list-divider {\n    border-color: #20273f;\n  }\n\n  .prev-btn {\n    &__icon {\n      color: #8ea5c0;\n      background-color: #20273f;\n\n      &:hover {\n        background-color: #3d475d;\n      }\n    }\n\n    &__text {\n      color: #8ea5c0;\n    }\n  }\n\n  .youll-get-block {\n    &__title {\n      color: #8ea5c0;\n\n      svg {\n        fill: #fff;\n      }\n    }\n\n    &__chest {\n      color: #8ea5c0;\n    }\n  }\n\n  .video-guide-block {\n    &__title {\n      color: #8ea5c0;\n\n      svg {\n        fill: #fff;\n      }\n    }\n  }\n}\n", ".deposit-step-2 {\n  .deposit-block__col:first-child {\n    border: 1px solid rgba(69 74 86 / 50%);\n  }\n\n  .chests-wrap {\n    background-color: #171b2f;\n  }\n\n  .vload-link {\n    border-color: rgba(55 151 211 / 30%);\n    color: #3797d3;\n\n    &:hover {\n      border-color: #3797d3;\n    }\n  }\n}\n", ".deposit-step-3 {\n  .payment-complete {\n    border: 1px solid rgba(69 74 86 / 50%);\n\n    .header {\n      &__item {\n        &:last-child {\n          border: 1px solid rgba(69 74 86 / 50%);\n        }\n      }\n\n      &__title {\n        color: #8ea5c0;\n      }\n    }\n\n    .form {\n      &__title {\n        color: #8ea5c0;\n      }\n\n      &__input {\n        border: 1px solid #454a56;\n        color: #fff;\n        background-color: transparent;\n      }\n\n      &__btn {\n        background-color: #303650;\n\n        &:hover {\n          color: var(--po-ui-kit-tag-a-base);\n        }\n      }\n\n      &__text {\n        &_bright {\n          color: #fff;\n        }\n      }\n    }\n\n    .qr {\n      &__wrap {\n        background-color: #fff;\n      }\n\n      &__right {\n        color: #8ea5c0;\n      }\n    }\n\n    &__form,\n    &__qr {\n      background: #171b2f;\n    }\n  }\n}\n", ".deposit-step-4 {\n  &--fail {\n    .big-icon svg {\n      fill: #76292d;\n    }\n  }\n\n  &--success {\n    .info-block {\n      box-shadow: 0 5px 10px rgba(0 0 0 / 10%);\n      background-color: rgba(#1c2037, 0.95);\n\n      .colored {\n        color: #8ea5c0;\n      }\n    }\n\n    .other-actions__text {\n      color: #8ea5c0;\n    }\n\n    .big-icon svg {\n      fill: #025b44;\n    }\n  }\n}\n", ".deposit-steps {\n  .step {\n    &__number {\n      border: 1px solid #20273f;\n    }\n\n    &__text {\n      color: #7e91a7;\n    }\n\n    &__line {\n      background-color: #20273f;\n    }\n\n    &--checked {\n      .step {\n        &__number {\n          color: #fff;\n          background-color: #20273f;\n        }\n\n        &__text {\n          color: #025b44;\n        }\n\n        &__line {\n          background-color: #025b44;\n        }\n\n        &__icon--check {\n          svg {\n            fill: #025b44;\n          }\n        }\n      }\n    }\n\n    &--active {\n      .step {\n        &__number {\n          color: #fff;\n          background-color: #20273f;\n        }\n\n        &__text {\n          color: #fff;\n        }\n      }\n    }\n\n    &--fail {\n      .step {\n        &__text {\n          color: #76292d;\n        }\n\n        &__icon svg {\n          fill: #76292d;\n        }\n      }\n    }\n  }\n}\n", ".deposit-filters {\n  .filters-icon {\n    fill: #fff;\n  }\n\n  &__btn {\n    border: 1px solid #454a56;\n    background: #1d2130;\n    transition: border-color 0.3s, background-color 0.3s;\n\n    &:hover {\n      border: 1px solid #009af9;\n      background-color: #1d2130;\n    }\n\n    &.active {\n      border-color: #009af9;\n      background: #314463;\n    }\n  }\n}\n", ".payments-block {\n  &.sort1 {\n    .divider::after {\n      border-color: #20273f;\n    }\n\n    .item {\n      $border-color: #454a56;\n\n      border: 1px solid #{$border-color};\n\n      &__end {\n        border-top: 1px solid #{$border-color};\n      }\n\n      &__end-item {\n        color: #8ea5c0;\n\n        &:first-child {\n          border-right: 1px solid #{$border-color};\n        }\n      }\n\n      &:hover {\n        border: 1px solid #009af9;\n      }\n\n      &--active {\n        border-color: #009af9;\n      }\n    }\n  }\n\n  &.sort2 {\n    .divider {\n      background-color: #1c2036;\n    }\n\n    .item {\n      &:hover {\n        background-color: rgba(#2b476f, 50%);\n      }\n    }\n  }\n}\n", ".support-text {\n  a {\n    border-color: rgba(#3797d3, 30%);\n    color: #3797d3;\n\n    &:hover {\n      border-color: #3797d3;\n    }\n  }\n}\n", ".payment-info-block-deposit {\n  box-shadow: 0 5px 10px rgba(0 0 0 / 10%);\n  background-color: #1c2037;\n\n  &__block {\n    border: 1px solid #454a56;\n  }\n\n  &__k {\n    color: #8ea5c0;\n  }\n\n  &__text {\n    color: #8ea5c0;\n\n    span {\n      color: #fff;\n    }\n  }\n}\n", ".sum-offer-item {\n  color: #8ea5c0;\n  background-color: #293145;\n\n  &.active,\n  &:hover {\n    background-color: #364059;\n  }\n}\n", ".amount-chest-info {\n  &__title,\n  &__text {\n    color: #8ea5c0;\n  }\n}\n", ".message-trade-and-update {\n  background: linear-gradient(75deg, rgba(52 186 114 / 30%) 1.75%, rgba(52 186 114 / 0%) 34.25%), #20273f;\n}\n", ".message-repeat-max-deposit {\n  background: linear-gradient(75deg, rgba(230 118 230 / 30%) 1.75%, rgba(230 118 230 / 0%) 34.25%), #20273f;\n}\n", ".message-traders-box {\n  background: linear-gradient(75deg, rgba(0, 224, 255, 0.30) 1.75%, rgba(0, 224, 255, 0.00) 34.25%), #20273f;\n}\n", ".message-welcome-bonus {\n  background-color: #20273f;\n\n  &::after {\n    background: linear-gradient(180deg, #009af9 0%, #65c4ff 100%);\n    filter: blur(26px);\n  }\n}\n", ".message-monsters-promo {\n  background: #20273f;\n}\n", ".mt5-page {\n  .message {\n    border-color: rgb(0 154 249 / 50%);\n    background-color: rgb(49 68 99 / 70%);\n\n    &__cross {\n      color: #8ea5c0;\n    }\n  }\n\n  &__label {\n    color: #8ea5c0;\n  }\n\n  .block {\n    background: #23283b;\n  }\n\n  .form {\n    &__input {\n      background: #272f43;\n    }\n\n    &__link-show-password {\n      border-left: 1px solid rgb(90 106 129 / 60%) !important;\n\n      .fa {\n        color: rgb(90 106 129 / 100%);\n      }\n\n      &:hover {\n        .fa {\n          color: #ffff;\n        }\n      }\n    }\n  }\n\n  .apps {\n    &__in {\n      border: 1px solid #404d62;\n      color: #fff;\n      background: #272f43;\n\n      &:hover {\n        color: #fff;\n        background: #3d475d;\n      }\n    }\n  }\n\n  .interface-btn {\n    border: 1px solid #404d62;\n    color: #fff;\n    background: #272f43;\n\n    &:hover {\n      color: #fff;\n      background: #3d475d;\n    }\n  }\n\n  .account-info {\n    input {\n      background: transparent;\n    }\n  }\n\n  .copy-to-clipboard {\n    border: 1px solid #0099fa;\n    background: rgb(0 153 250 / 20%);\n\n    span {\n      color: #fff !important;\n    }\n\n    &:hover {\n      background: rgb(0 153 250 / 40%);\n    }\n  }\n\n  &__notification::before {\n    color: #166ba8 !important;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.achievements-page {\n  .rw > div {\n    background-color: settings(dark-blue, page, block, bgColor);\n  }\n\n  .title-type {\n    span {\n      color: #fff;\n    }\n\n    &--bronze::before {\n      box-shadow: 0 0 18px 2px rgb(177 119 94 / 90%);\n    }\n\n    &--silver::before {\n      box-shadow: 0 0 18px 2px rgba(#76797c, 0.9);\n    }\n\n    &--gold::before {\n      box-shadow: 0 0 18px 2px rgba(#ffa031, 0.9);\n    }\n  }\n\n  .l .exp__in {\n    color: #fff;\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.page-faq {\n  .svg-icon {\n    fill: #92a5c0;\n  }\n\n  .category {\n    color: settings(dark-blue, listLinks, color);\n    background-color: settings(dark-blue, listLinks, bgColor);\n\n    &__text-h {\n      color: #fff;\n    }\n\n    &--active,\n    &:hover {\n      background-color: settings(dark-blue, listLinks, item-blue--hover, bgColor);\n    }\n  }\n}\n", ".rw {\n  > .l,\n  > .r,\n  > .l > div {\n    background-color: #131628b3;\n  }\n\n  > .r {\n    color: #b2bacd;\n\n    h1,\n    h2,\n    h3,\n    h4 {\n      color: #fff;\n    }\n  }\n\n  a {\n    color: #8ea5c0;\n\n    &:hover {\n      color: #5e7ea3;\n    }\n  }\n\n  .read-more-wrap .btn {\n    color: #fff;\n  }\n}\n\n.nav-btn {\n  border: 3px solid rgb(38 44 65);\n  color: #7e91a7;\n  background-color: #20273f;\n}\n", "@use \"sass:meta\";\n\n.tutorial {\n  @include meta.load-css(\"guide-common\");\n\n  .rw > .l {\n    .root-title {\n      color: #fff;\n\n      &:hover {\n        color: #5e7ea3;\n      }\n    }\n\n    li.active > a {\n      position: relative;\n      color: #fff;\n\n      &::after {\n        content: \"\";\n        position: absolute;\n        left: -7px;\n        right: -7px;\n        top: -5px;\n        bottom: -5px;\n        z-index: -1;\n        display: block;\n        background-color: rgb(143 165 191 / 40%);\n      }\n    }\n  }\n}\n", ".forex-glossary-page {\n  .panel {\n    background-color: #131628;\n\n    .panel-body {\n      background-color: transparent;\n    }\n  }\n\n  .back-btn {\n    border-color: #293145;\n    color: #fff;\n    background-color: #162032;\n\n    &:hover {\n      background-color: #293145;\n    }\n  }\n}", "@use \"@mixins/settings\" as *;\n\n.achievements-market {\n  .l,\n  .r {\n    background-color: settings(dark-blue, panel, body, bgColor);\n  }\n\n  .balance {\n    color: #ffde74;\n  }\n\n  .market-buttons a.market-button {\n    &.no-active {\n      border: 1px solid #a5a8b3;\n      box-shadow: 0 1px 2px rgba(#fff, 0.2), 0 0 15px rgba(#fff, 0.2);\n      background-color: rgba(#454851, 0.3);\n\n      &.sell {\n        &:focus,\n        &:hover {\n          border-color: #ffe64c;\n          box-shadow: 0 1px 2px rgb(0 0 0 / 30%), 0 0 15px rgb(231 206 69 / 60%);\n          background-color: rgb(231 206 69 / 20%);\n        }\n      }\n\n      &.buy {\n        &:focus,\n        &:hover {\n          border-color: #5ad75a;\n          box-shadow: 0 1px 2px rgb(0 0 0 / 30%), 0 0 15px rgb(94 222 94 / 60%);\n          background-color: rgb(94 222 94 / 20%);\n        }\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.achievements-purchases {\n  .list-purchases .table__title-link {\n    color: #fff;\n  }\n\n  .list-purchases .tabsp__item.tabsp__item {\n    color: #fff;\n  }\n\n  .list-purchases .tabsp__item--selected.tabsp__item {\n    &--available {\n      background-color: rgba(#449d44, 0.4);\n    }\n\n    &--active {\n      background-color: rgba(#ffa031, 0.4);\n    }\n\n    &--used {\n      background-color: rgba(#c9302c, 0.4);\n    }\n  }\n\n  .list-purchases {\n    &__item {\n      border-radius: 10px;\n      background-color: settings(dark-blue, panel, body, bgColor);\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.gems-lotto {\n  .panel {\n    box-shadow: none;\n  }\n\n  .rw {\n    > .l,\n    > .r {\n      background-color: settings(dark-blue, panel, body, bgColor);\n    }\n  }\n\n  .stats > p {\n    color: #ffde74;\n  }\n\n  .gems-lotto-head {\n    .message {\n      color: #fff;\n    }\n\n    .gems-lotto-head__title {\n      color: #fff;\n    }\n  }\n\n  &__status {\n    color: #fff;\n\n    &--on {\n      border-color: #035843;\n      background: rgb(3 88 67 / 20%);\n\n      svg {\n        color: #32ac41;\n      }\n    }\n\n    &--off {\n      border-color: rgb(83 85 98 / 50%);\n      background: rgb(83 85 98 / 10%);\n\n      svg {\n        color: #535562;\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.mining-page {\n  .rw {\n    > .l,\n    > .r {\n      border-radius: 10px;\n      background-color: settings(dark-blue, page, block, bgColor);\n    }\n\n    > .r h3 {\n      color: #fff;\n    }\n  }\n\n  .mining-stats-block__title {\n    color: #ffde74;\n  }\n\n  .text {\n    color: #8fa5bf;\n  }\n\n  .license-table {\n    .license-active {\n      background-color: #33374e;\n    }\n  }\n}\n", ".deposit-compare {\n  .item {\n    &__line {\n      border-bottom: 1px solid #45484e;\n    }\n  }\n}", "@use \"sass:meta\";\n\n.page-user-manual {\n  @include meta.load-css(\"guide-common\");\n\n  .rw {\n    .root-title {\n      color: #fff;\n\n      &:hover {\n        color: #5e7ea3;\n      }\n    }\n\n    > .l li.active > a {\n      position: relative;\n      color: #fff;\n\n      &::after {\n        content: \"\";\n        position: absolute;\n        left: -7px;\n        right: -7px;\n        top: -5px;\n        bottom: -5px;\n        z-index: -1;\n        display: block;\n        background-color: rgb(143 165 191 / 40%);\n      }\n    }\n\n    > .l,\n    > .r,\n    > .l > div {\n      border-radius: 10px;\n    }\n  }\n}\n", "@use \"@config/default\";\n\n.affiliate-page {\n  &__in {\n    background-color: rgba(#131628, 0.9);\n  }\n\n  .promo {\n    border-color: #20273f;\n\n    &__title {\n      color: #b2bacd;\n    }\n  }\n\n  .svg-circle {\n    stroke: #8c96b2;\n  }\n\n  .svg-icon {\n    fill: #b2bacd;\n  }\n\n  .text {\n    a {\n      border-bottom: 1px solid rgb(116 125 152 / 100%);\n      color: #747d98;\n\n      &:hover {\n        border-bottom: 1px solid rgb(116 125 152 / 50%);\n      }\n    }\n\n    &__italic {\n      color: #747d98;\n    }\n  }\n\n  .advantages {\n    &__in {\n      border-color: #20273f;\n      background-color: #161a2b;\n    }\n\n    &__list {\n      li {\n        &::before {\n          background-image: url(\"#{default.$svgDir}/icons/affiliate-program/check.svg\");\n        }\n      }\n\n      &_dotted {\n        li {\n          &::before {\n            background-image: url(\"#{default.$svgDir}/icons/affiliate-program/circle.svg\");\n          }\n        }\n      }\n    }\n  }\n\n  .form-block {\n    border-color: #20273f;\n    background-color: #161a2b;\n  }\n\n  .form {\n    .form-control {\n      border-color: #293145;\n    }\n  }\n\n  .privacy-policy {\n    color: #9396a0;\n\n    a {\n      border-color: rgb(147 150 150 / 100%);\n      color: #9396a0;\n\n      &:hover {\n        border-color: rgb(147 150 150 / 50%);\n      }\n    }\n\n    label {\n      &::before {\n        border-color: #293145;\n        background-color: #162032;\n      }\n\n      &::after {\n        background-image: url(\"#{default.$svgDir}/icons/affiliate-program/check.svg\");\n      }\n    }\n  }\n}\n", ".new-year-lottery {\n  .rw {\n    > .l,\n    > .r {\n      box-shadow: 0 0 15px 2px #181819;\n    }\n  }\n}", "@use \"sass:color\";\n@use \"@mixins/settings\" as *;\n\n.supportv2-page {\n  &__in {\n    background-color: rgba(#131628, 0.9);\n  }\n\n  .menu-block {\n    &__link {\n      background-color: #293145;\n      transition: background-color settings(common, transitionTime);\n\n      &:hover {\n        background-color: color.adjust(#293145, $lightness: -1%);\n      }\n    }\n\n    &__text {\n      color: #fff;\n    }\n  }\n\n  .form-block {\n    input[type=\"file\"] + label {\n      border-color: #495671;\n      color: #7e91a7 !important;\n      background-color: #162032 !important;\n    }\n  }\n\n  .video-btn {\n    color: #fff;\n  }\n\n  .answer {\n    &__actions a {\n      color: #455165;\n\n      &:hover {\n        color: color.adjust(#455165, $lightness: 5%);\n      }\n\n      &.active {\n        color: #c2cbda;\n      }\n    }\n\n    & + & {\n      border-top: 1px solid #2d3142;\n    }\n  }\n\n  &.ticket {\n    .table {\n      td {\n        color: #b5b7bc;\n      }\n    }\n\n    .attachments-info {\n      b {\n        color: #8fa5bf;\n      }\n    }\n  }\n\n  &.create {\n    .items {\n      border: 1px solid #2c3041;\n      border-radius: 4px;\n    }\n\n    .item {\n      background-color: #141729;\n      cursor: pointer;\n\n      &__count {\n        color: #d6d7da;\n        background-color: #20273f;\n      }\n\n      &__arrow {\n        color: #7589a0;\n      }\n\n      &:hover,\n      &--active {\n        background-color: #20273f;\n\n        .item__title {\n          color: #fff;\n        }\n\n        .item__count {\n          background-color: #20273f;\n        }\n      }\n    }\n\n    .search-form-block {\n      .search-results-block {\n        background-color: #262c41;\n      }\n\n      .search-results {\n        a {\n          color: #7e91a7;\n        }\n\n        li {\n          &:hover {\n            background-color: #2d354e;\n          }\n        }\n\n        &__c {\n          color: #dbdcde;\n        }\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.achievements-history {\n  &__in {\n    border-radius: 10px;\n    background-color: settings(dark-blue, panel, body, bgColor);\n  }\n\n  .top-text {\n    color: #8fa5bf;\n  }\n\n  .rewards-table {\n    tr {\n      > td,\n      > th {\n        background-color: rgba(#292d4a, 0.5);\n      }\n    }\n  }\n}\n", ".achievements-rating {\n  &__in {\n    border-radius: 10px;\n    background-color: rgba(#131628, 0.7);\n  }\n\n  .top-text {\n    color: #8fa5bf;\n  }\n\n  .rating-table {\n    thead th {\n      background-color: #1c2034;\n    }\n  }\n\n  .rewards-table {\n    tr {\n      > td,\n      > th {\n        background-color: rgba(#292d4a, 0.5);\n      }\n    }\n  }\n\n  .period-block {\n    &__item {\n      background-color: #1c2034;\n\n      &:hover,\n      &:focus,\n      &--active {\n        color: #fff;\n        background-color: #434858;\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.community-help {\n  .l,\n  .r {\n    background-color: settings(dark-blue, page, block, bgColor);\n  }\n\n  .current-request {\n    border: 1px solid #2b3049;\n    border-radius: settings(common, borderRadius);\n    background-color: rgba(#1d2034, 0.3);\n  }\n}\n", ".withdrawal {\n  .ajax-loader-wrapper {\n    background-color: rgba(#0f101d, 0.8);\n  }\n}\n", ".my-safe-page {\n  .term-of-use {\n    &__desc {\n      color: #8ea5c0;\n    }\n\n    &.open > p {\n      color: #798ba3;\n    }\n  }\n\n  .chart-bg {\n    background-color: #161a2b;\n  }\n\n  .history-block {\n    .panel-body {\n      background-color: #161a2b;\n    }\n  }\n\n  .safes-tabs {\n    .btn {\n      &__in {\n        background-color: #161a2b;\n\n        &:hover,\n        &.active {\n          background-color: #111424;\n        }\n\n        @media screen and (max-width: 650px) {\n          background-color: #111424;\n\n          &:hover,\n          &.active {\n            background-color: #161a2b;\n          }\n        }\n      }\n    }\n\n    &__block {\n      background-color: #121424;\n\n      @media screen and (max-width: 650px) {\n        background-color: #161a2b;\n\n        .block__col:nth-child(odd) {\n          background-color: #111424;\n        }\n      }\n    }\n\n    .block {\n      &__col {\n        &_first {\n          color: #7e91a7;\n        }\n\n        > div {\n          background-color: #161a2b;\n\n          &:nth-child(3) {\n            background-color: transparent;\n          }\n\n          @media screen and (max-width: 650px) {\n            background-color: transparent;\n          }\n        }\n\n        &_info {\n          > div {\n            &:last-child {\n              background-color: transparent;\n            }\n          }\n\n          &:hover {\n            box-shadow: 0 20px 40px rgb(6 8 22 / 40%);\n\n            > div {\n              background-color: #181d30;\n\n              &:last-child {\n                background-color: transparent;\n              }\n\n              &:nth-child(3) {\n                background-color: transparent;\n              }\n            }\n          }\n        }\n      }\n\n      &__header {\n        background: transparent !important;\n      }\n\n      &__mob-label {\n        color: #7e91a7;\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.my-safe-page-window-1 {\n  .content__in {\n    background-color: settings(dark-blue, panel, body, bgColor);\n  }\n\n  .types-of-safes {\n    &__in {\n      border: 1px solid #20273f;\n    }\n\n    &__header {\n      background-color: #20273f;\n    }\n  }\n\n  .calculate-investment {\n    &__result-val {\n      color: #025b44;\n    }\n\n    &__divider {\n      background-color: #20273f;\n    }\n\n    &__in {\n      border: 1px solid #20273f;\n      background-color: #161a2b;\n    }\n\n    &__title {\n      color: #7e91a7;\n    }\n  }\n\n  .form-group label {\n    color: #7e91a7;\n  }\n\n  .bootstrap-select .dropdown-toggle.btn-default,\n  input.form-control {\n    border-color: #20273f !important;\n    background-color: #161a2b !important;\n\n    .caret {\n      color: #7e91a7;\n    }\n  }\n\n  .bootstrap-select .dropdown-toggle.btn-default,\n  input.form-control,\n  .input-group {\n    border-color: #20273f !important;\n    background-color: #161a2b !important;\n\n    .caret {\n      color: #7e91a7;\n    }\n\n    .input-group-addon {\n      border-left: 1px solid #293145;\n      font-weight: 700;\n      color: #fff;\n      background-color: transparent;\n    }\n  }\n\n  .bootstrap-select .dropdown-toggle.btn-default,\n  input.form-control,\n  .input-group {\n    border-color: #20273f !important;\n    background-color: #161a2b !important;\n\n    .caret {\n      color: #7e91a7;\n    }\n\n    .input-group-addon {\n      border-left: 1px solid #293145;\n      font-weight: 700;\n      color: #fff;\n      background-color: transparent;\n    }\n  }\n\n  .bootstrap-select .dropdown-toggle.btn-default,\n  input.form-control,\n  input.form-control::placeholder {\n    color: #fff !important;\n  }\n\n  .detailing-of-charges {\n    &__btn {\n      border: 1px solid #20273f;\n      color: #7e91a7;\n      background-color: #161a2b;\n    }\n\n    &__table {\n      thead th {\n        border: 0 !important;\n        color: #fff;\n        background-color: #161a2b;\n      }\n\n      tbody td {\n        border-color: #16192c;\n        background-color: #121424;\n      }\n\n      tfoot tr {\n        background-color: #161a2b;\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.my-safe-page-window-2 {\n  .information-on-your-current-safe-account {\n    &__item {\n      border: 1px solid #20273f;\n      background-color: #161a2b;\n    }\n  }\n\n  .sub-title .fa {\n    color: #8ea5c0;\n  }\n\n  .text-block {\n    &__text {\n      color: #8fa5bf;\n    }\n  }\n\n  .gr {\n    &__section {\n      background-color: #293145;\n    }\n\n    &__section--darker .gr__percent {\n      color: #475473;\n    }\n\n    &__divider--darker {\n      background-color: #475473;\n    }\n\n    .fa-lock {\n      color: #475473;\n    }\n  }\n\n  .history-block {\n    background-color: settings(dark-blue, panel, body, bgColor);\n  }\n\n  .rw > .l,\n  .information-on-your-current-safe-account,\n  .safe-conditions {\n    background-color: settings(dark-blue, panel, body, bgColor);\n  }\n\n  .available-safes {\n    &__item {\n      border: 1px solid #293145;\n      background-color: #161a2b;\n      transition: background-color 0.3s, border-color 0.3s;\n\n      &:hover {\n        border-color: #333c51;\n        background-color: #1e2338;\n      }\n    }\n\n    &__item--active {\n      border-color: #025b44;\n      box-shadow: 0 0 18px rgba(38 176 72 / 23%);\n\n      .available-safes__info-icon {\n        color: #fff;\n        background: #0c7245;\n        background: linear-gradient(180deg, #37b148, #1b6143);\n      }\n    }\n  }\n\n  .history-block {\n    .panel-body {\n      border: 1px solid #20273f;\n    }\n  }\n\n  .l .title a {\n    background-color: #20273f;\n  }\n\n  .safe-deposit-balance-chart-block {\n    &__btn {\n      background-color: rgb(41, 49, 69);\n    }\n  }\n\n  .safes-tabs {\n    &__block {\n      background-color: transparent;\n    }\n\n    .block__col_info {\n      > div:last-child {\n        background-color: #161a2b;\n\n        @media screen and (max-width: 650px) {\n          background-color: transparent;\n        }\n      }\n\n      &:hover > div:last-child {\n        background-color: #181d30;\n\n        @media screen and (max-width: 650px) {\n          background-color: transparent;\n        }\n      }\n    }\n  }\n}\n", "@use \"@mixins/settings\" as *;\n\n.my-safe-page-plug {\n  .content__in {\n    background-color: settings(dark-blue, panel, body, bgColor);\n  }\n}\n", "@use \"@mixins/settings\" as *;\n@use \"@scss/desktop.blocks/pages/social-trading-rewards-page-mixins\" as *;\n\n.social-trading-rewards-page {\n  .summary {\n    &__text {\n      color: #8ea5c0;\n    }\n  }\n\n  .str-text {\n    &--other-color {\n      color: #8ea5c0;\n    }\n  }\n\n  .si-progress {\n    background-color: #131628;\n  }\n\n  .shard-info {\n    background-color: #202840;\n\n    &__text-small {\n      color: #8ea5c0;\n    }\n\n    &__btn {\n      color: #fff;\n    }\n\n    &--red {\n      &::after {\n        background: hsla(261, 33%, 19%, 1);\n        background: linear-gradient(135deg, #202840 0%, #2d2040 32%, #d64348 100%);\n      }\n\n      .si-progress__in {\n        background: rgb(225, 77, 82);\n        background: linear-gradient(90deg, rgba(225, 77, 82, 1) 0%, rgba(157, 18, 23, 1) 100%);\n      }\n    }\n\n    &--blue {\n      &::after {\n        background: hsla(226, 33%, 19%, 1);\n        background: linear-gradient(135deg, #202840 0%, hsla(226, 33%, 19%, 1) 32%, hsla(201, 67%, 73%, 1) 100%);\n      }\n\n      .si-progress__in {\n        background: rgb(143, 204, 236);\n        background: linear-gradient(90deg, rgba(143, 204, 236, 1) 0%, rgba(72, 130, 161, 1) 100%);\n      }\n    }\n\n    &--green {\n      &::after {\n        background: hsla(226, 33%, 18%, 1);\n        background: linear-gradient(135deg, #202840 0%, hsla(226, 33%, 19%, 1) 32%, hsla(163, 56%, 51%, 1) 100%);\n      }\n\n      .si-progress__in {\n        background: rgb(74, 230, 185);\n        background: linear-gradient(90deg, rgba(74, 230, 185, 1) 0%, rgba(30, 124, 97, 1) 100%);\n      }\n    }\n  }\n\n  .collect-block {\n    &__info-text-small {\n      color: #8ea5c0;\n    }\n  }\n\n  .tt-slider {\n    &__item {\n      border-color: #292d4a;\n    }\n\n    &__item-small-text {\n      color: #8ea5c0;\n    }\n  }\n\n  .swiper-pagination {\n    &-bullet {\n      background-color: #20273f;\n    }\n\n    &-bullet-active {\n      background-color: #0099fa;\n    }\n  }\n\n  .l,\n  .r {\n    background-color: settings(dark-blue, page, block, bgColor);\n  }\n\n  @media screen and (max-width: 1135px) {\n    @include shard-info-colored-dark-blue;\n  }\n}\n", "@mixin shard-info-compact {\n  .shard-info {\n    flex-wrap: wrap;\n\n    .si-progress {\n      height: 15px;\n    }\n\n    &__shard {\n      order: 1;\n    }\n\n    &__btn-block {\n      order: 2;\n    }\n\n    &__text-wrap {\n      flex-basis: 100%;\n      order: 3;\n    }\n\n    &__progress {\n      flex-basis: calc(100% - 75px);\n      order: 4;\n      margin: 10px 0 0;\n    }\n\n    &__stats {\n      order: 5;\n      margin: 7px 0 0;\n      width: 75px;\n      font-size: 13px;\n    }\n\n    &__btn {\n      border-radius: 4px;\n      padding: 6px 10px;\n      min-width: auto;\n    }\n\n    &__btn-text1 {\n      display: none;\n    }\n\n    &__btn-text2 {\n      display: block;\n      font-size: 12px;\n    }\n\n    &--bordered::after {\n      right: -150px;\n      margin-top: 0;\n      width: 370px;\n      height: 280px;\n    }\n  }\n}\n\n@mixin shard-info-colored-dark-blue {\n  .shard-info--red .shard-info__btn {\n    background: rgb(228 80 85);\n    background: linear-gradient(180deg, rgb(228 80 85 / 100%) 0%, rgb(146 9 14 / 100%) 100%);\n  }\n\n  .shard-info--blue .shard-info__btn {\n    background: rgb(119 197 231);\n    background: linear-gradient(180deg, rgb(119 197 231 / 100%) 0%, rgb(71 164 205 / 100%) 100%);\n  }\n\n  .shard-info--green .shard-info__btn {\n    background: rgb(75 229 185);\n    background: linear-gradient(180deg, rgb(75 229 185 / 100%) 0%, rgb(46 163 130 / 100%) 100%);\n  }\n}\n\n@mixin shard-info-colored-light {\n  .shard-info--red .shard-info__btn {\n    background-color: #cf3d42;\n  }\n\n  .shard-info--blue .shard-info__btn {\n    background-color: #7ac6e8;\n  }\n\n  .shard-info--green .shard-info__btn {\n    background-color: #2da080;\n  }\n}\n", ".profile {\n  &__languages {\n    .active a {\n      border: 1px solid #009af9;\n      background-color: #314463;\n    }\n\n    li:not(.active) a:hover {\n      background-color: rgba(142 165 192 / 10%);\n    }\n  }\n}\n", ".mt5-trading-panel-modal {\n  background-color: #23283b;\n\n  .tab-wrap {\n    background-color: #2b3145 !important;\n  }\n\n  .mt5-conditions-for-closing-a-deal {\n    &__in {\n      background-color: #212535;\n    }\n  }\n\n  .tab-nav li a {\n    background-color: #222639;\n  }\n\n  .order-types {\n    background-color: #2a3145;\n  }\n\n  .transaction-volume-types {\n    background-color: #222638;\n  }\n\n  .lot {\n    &__number-wrap {\n      color: #9396a0;\n    }\n  }\n}\n", "// темы с разделов: история, отложенные ордера и позиции\n.mt5-pp-item {\n  &__title {\n    background-color: #1e2232;\n\n    span {\n      color: #9396a0;\n    }\n  }\n\n  &__content {\n    background-color: #212536;\n  }\n\n  .info {\n    &__key {\n      color: #9396a0;\n    }\n\n    &__val {\n      span {\n        color: #9396a0;\n      }\n    }\n  }\n}", ".mt5-modal-top {\n  border-bottom: 1px solid #292d3f;\n  background-color: #222639;\n\n  .sort {\n    color: #9396a0;\n  }\n}"], "names": [], "sourceRoot": ""}