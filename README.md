# Pocket Option Trading Bot

An automated trading bot for the Pocket Option platform that uses WebSocket connections for real-time market data and DOM manipulation for executing trades.

## Features

- **Real-time Market Data**: Connects to Pocket Option's WebSocket for live price feeds
- **Automated Trading**: Executes buy/sell orders based on technical analysis
- **Multiple Strategies**: Trend following, mean reversion, RSI, and MACD strategies
- **Risk Management**: Position sizing, stop-loss, and daily loss limits
- **DOM Manipulation**: Controls the trading interface programmatically
- **Session Persistence**: Automatically saves and restores login sessions
- **Comprehensive Logging**: Tracks all trades and bot activities
- **Demo Account Support**: Safely test strategies on demo accounts

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd pocket-bot-v2
```

2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file for environment variables (optional):

```bash
# Add any environment-specific configurations
NODE_ENV=development
```

## Configuration

Edit `src/config.js` to customize the bot settings:

### Trading Settings

- `defaultAmount`: Default trade amount
- `maxAmount`: Maximum trade amount
- `defaultAsset`: Default trading asset (e.g., 'AUD/CAD OTC')
- `defaultTimeframe`: Default timeframe (M8, M10, M30, H1, H4, D1, D7, D14)
- `maxConcurrentTrades`: Maximum number of simultaneous trades

### Strategy Settings

- `type`: Strategy type ('trend_following', 'mean_reversion', 'scalping')
- `indicators`: Configure technical indicators (MA, RSI, Bollinger Bands)

### Risk Management

- `maxDailyLoss`: Maximum daily loss limit
- `maxConsecutiveLosses`: Stop trading after consecutive losses
- `riskPercentage`: Percentage of balance to risk per trade

## Usage

### Start the Bot

```bash
npm start
```

### Development Mode (with auto-restart)

```bash
npm run dev
```

### Run Tests

```bash
npm test
```

## Session Management

The bot automatically saves your login session so you don't have to log in every time you restart it.

### How Session Persistence Works

1. **First Run**: The bot opens a browser and waits for you to log in manually
2. **Session Save**: Once logged in, your session (cookies, local storage) is automatically saved
3. **Subsequent Runs**: The bot automatically restores your session and logs you in
4. **Session Expiry**: Sessions expire after 24 hours for security

### Session Management Commands

```bash
# Check current session status
npm run session:info

# Clear saved session (force re-login)
npm run session:clear

# Export session for backup
npm run session:export ./my-session-backup.json

# Import session from backup
npm run session:import ./my-session-backup.json
```

### Session Storage Location

Sessions are stored in the `./browser-data/` directory:

- `./browser-data/session.json` - Session data (cookies, storage)
- `./browser-data/` - Browser profile data

### Manual Session Management

You can also use the session CLI directly:

```bash
# Show session information
node src/session-cli.js info

# Clear session
node src/session-cli.js clear

# Export/import sessions
node src/session-cli.js export ./backup.json
node src/session-cli.js import ./backup.json
```

## Architecture

### Core Components

1. **Bot (`src/bot.js`)**: Main orchestrator that coordinates all components
2. **WebSocket Client (`src/websocket-client.js`)**: Handles real-time market data
3. **DOM Controller (`src/dom-controller.js`)**: Controls the browser and trading interface
4. **Trading Strategy (`src/trading-strategy.js`)**: Implements trading algorithms
5. **Logger (`src/utils/logger.js`)**: Comprehensive logging system
6. **Config (`src/config.js`)**: Centralized configuration

### Data Flow

1. WebSocket receives real-time price data
2. Trading strategy analyzes market conditions
3. If signal is generated, DOM controller executes trade
4. Trade results are tracked and logged
5. Risk management rules are applied

## Trading Strategies

### Trend Following

- Uses moving averages (SMA/EMA) to identify trends
- Buys when price is above moving averages
- Sells when price is below moving averages

### Mean Reversion

- Uses Bollinger Bands to identify overbought/oversold conditions
- Sells when price touches upper band
- Buys when price touches lower band

### RSI Strategy

- Uses Relative Strength Index for momentum analysis
- Sells when RSI > 70 (overbought)
- Buys when RSI < 30 (oversold)

### MACD Strategy

- Uses MACD crossovers for trend changes
- Buys on bullish crossover
- Sells on bearish crossover

## Risk Management

The bot includes several risk management features:

- **Position Sizing**: Calculates trade amount based on account balance
- **Daily Loss Limit**: Stops trading if daily loss exceeds limit
- **Consecutive Loss Protection**: Pauses trading after consecutive losses
- **Maximum Concurrent Trades**: Limits number of simultaneous positions
- **Time-based Filters**: Minimum time between trades

## Logging

The bot provides comprehensive logging:

- **Console Logs**: Real-time status and debug information
- **File Logs**: Persistent logs saved to `./logs/` directory
- **Trade Logs**: Detailed trade execution and results
- **Error Logs**: Error tracking and debugging

## Browser Automation

The bot uses Puppeteer for browser automation:

- **Headless Mode**: Can run with or without visible browser
- **Developer Tools**: Access to browser dev tools for debugging
- **Screenshot Capture**: Take screenshots for monitoring
- **WebSocket Monitoring**: Intercepts WebSocket connections

## Safety Features

- **Demo Account**: Automatically switches to demo account
- **Error Handling**: Comprehensive error handling and recovery
- **Graceful Shutdown**: Properly closes trades and connections
- **Rate Limiting**: Prevents excessive API calls

## Monitoring

Monitor the bot's performance:

```javascript
// Get bot status
const status = bot.getStatus()
console.log(status)

// Take screenshot
await bot.takeScreenshot()
```

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**

   - Check if Pocket Option is accessible
   - Verify WebSocket URL in config
   - Check network connectivity

2. **DOM Elements Not Found**

   - Pocket Option may have updated their interface
   - Update selectors in `dom-controller.js`
   - Check browser console for errors

3. **Trades Not Executing**
   - Verify demo account is selected
   - Check trade amount and balance
   - Review risk management settings

### Debug Mode

Enable debug logging:

```javascript
// In config.js
logging: {
	level: 'debug'
}
```

## Disclaimer

This bot is for educational and testing purposes only. Trading involves risk, and you should never trade with money you cannot afford to lose. Always test thoroughly on demo accounts before considering live trading.

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions:

1. Check the troubleshooting section
2. Review the logs for error messages
3. Create an issue with detailed information
