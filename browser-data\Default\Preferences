{"account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 0}, "autocomplete": {"retention_policy_last_version": 121}, "autofill": {"last_version_deduped": 121}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1022, "left": 10, "maximized": true, "right": 1520, "top": 10, "work_area_bottom": 1032, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "countryid_at_install": 20552, "devtools": {"adb_key": "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDDcbAIb09bnpHG4Sbpc/LnxXG2BObylzjVYm6jYtRYOpkuJglio6mQO20txtmfVOI5ltCGRjWMEfpogffY3jPh8kZeIUBzt3axzdDM65dvm8az1J10CLUy21jXzs7Vrxjj6zetbUrrB06VstcHORxDfkpy+IZIbt1V3qi3i68ahE4B7qcJHCOtINFEg6jFt/mDRPgRcyjknbMf8UcDY68i6X6c2lQIigNJ4U2pZbwFO+fhv+lyVs0P/0P2mjgJB9oCWGkic/9V3ziVNAjJitygDBh8i+7VAqQ1VazgOPSLRG9H0/hVGV+qnliKZfWh9ummVk1Cz5OcLNyCiccb3vYnAgMBAAECggEAPghaiALUXE8TE92onRZZNakcc1pQ9xR6THgGh8M5hJtsvRIDjpRFoGrMN2hS8FhfyUmN++BS5JdcNEl0Ihg8vW1JWl0ReOj1OnAJ4tCv6xO1WKSWKRsOcArgwvZ3/aiqrMSekmQLEFfsYGU4DKnax61Q8fbhoCHYDIY0ZLVoKXDgviImjTxxPy301RSeh/X+l9jnVmls7NFSZGJl/ui8vQUWO6QdYECdnGzyCU3KeyRwh+rQyvRnmZHN7v/tXGiOsY3YXlzqkGBF2Tkyt/IGLj+5I919qa/2UOROjptm93VI2/KP2e79gsUV0mYZMWnudSqLA70X3iMF26fIUutz4QKBgQDoL/7fnZ0MMyNDS0rssE/kMaLtV2mg2nPuensix9Mqx4wd1Ob0m/nEOJcR1aEzmJF/yWPVdNMxWfQQ6EtovJxpOB1rJoytx1bU0aSwndOe84CA+jzV0ll5sYM2i9CXGewsg8M9R8IDiXsjOy2iWHu0ppi6euRrMg6V0A9rzbdndwKBgQDXfQGztiObac3aJEdSygsx3Gn+OyqPPIIq32sPeqdsxGVM+3bDXZLR+hWQ4ObJRXsW/9CnjiULxH/mx2Vji7QsnpuBXz41Jfxn7g5H0z5CtEmiH3Aygi3l6r0dvohFL+rJBWxVu+USF8bcXC4tUDYz4+CSkXh8MsyYEDm+oJPy0QKBgCnNUk7CGp8yqbuY16FrUMbdpN6kKTE7gznBNWAap8sA6BoK1ZXeawZHxfMBCie25SMjh+TLRqaQ8l8E42a05lgPMUS0SQmUZO6l28escogggNc3tjIsz80YB26Pl3tMwHd3NXRX8fcfDltWcS3UTllE0SRpsawW9AdG0S/wdn+bAoGAZYm44JaFprivUAfiN8eOiNskcrAseLv5VfxatTuFYsIUPbcMGjigcyLVQKRI+zQrMQYvqpRXLnlUD0mLo+Fzb8b43XgkluSkT9CjmAKZBtNvEt6ffp1KxVjb7gUWrwtKJZjRmIBmCyV7BeDFvYJbWA4bhtTXIibJsymKHCIMNNECgYEAxvD+nIlqdNMYOwqqkURtjzY3h7ywvalvlXLvKMEj0NBOZUjGZDNL9srg5gvTTU8New7SrEtWPQpq7WSX1Ey8DFI4GXrdo8pWGqzqPOAGc5S+/vvMgZNVW9ss39Rn+DfypDKhKQTKe5HqH8h4KgFyerAKcF6mKikOBKkzXtlxNnI=", "preferences": {"Inspector.drawerSplitViewState": "{\"horizontal\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "InspectorView.splitViewState": "{\"vertical\":{\"size\":0}}", "Styles-pane-sidebar-tabOrder": "{\"Styles\":10,\"Computed\":20}", "closeableTabs": "{\"security\":true,\"chrome_recorder\":true}", "currentDockState": "\"right\"", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "filterBar-networkPanel-toggled": "false", "inspectorVersion": "36", "networkPanelSidebarState": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "networkPanelSplitViewState": "{\"vertical\":{\"size\":0}}", "networkPanelSplitViewWaterfall": "{\"vertical\":{\"size\":0}}", "networkShowSettingsToolbar": "false", "panel-selectedTab": "\"network\""}, "synced_preferences_sync_disabled": {"adornerSettings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false}]", "syncedInspectorVersion": "36"}}, "dips_timer_last_update": "13392904400904661", "domain_diversity": {"last_reporting_timestamp": "13392900564812480"}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "121.0.6167.85", "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "13392900564195392", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13392900564195392", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Chromium.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "page_ordinal": "n", "path": "C:\\Users\\<USER>\\.cache\\puppeteer\\chrome\\win64-121.0.6167.85\\chrome-win64\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "13392900564196150", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13392900564196150", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chromium PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Users\\<USER>\\.cache\\puppeteer\\chrome\\win64-121.0.6167.85\\chrome-win64\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gaia_cookie": {"changed_time": **********.502021, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.googlechromefortesting.windows"}, "google": {"services": {"signin_scoped_device_id": "0530e023-32d2-4142-a6a8-2df939a5e8ad"}}, "in_product_help": {"session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"engagement": {"schema_version": 5}}, "ntp": {"num_personal_suggestions": 3}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"enable_quiet_permission_ui_enabling_method": {"notifications": 1}, "exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "access_to_get_all_screens_media_in_session": {}, "accessibility_events": {}, "anti_abuse": {}, "app_banner": {"https://pocketoption.com:443,*": {"last_modified": "*****************", "setting": {"https://pocketoption.com/": {"next_install_text_animation": {"delay": "86400000000", "last_shown": "13392900570169674"}}, "https://pocketoption.com/en/cabinet?source=pwa": {"couldShowBannerEvents": 1.3392900570170002e+16}}}}, "ar": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "client_hints": {}, "clipboard": {}, "cookie_controls_metadata": {"https://[*.]pocketoption.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {"https://pocketoption.com:443,*": {"last_modified": "*****************", "setting": {"UserDataFieldFilled": true}}}, "geolocation": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "legacy_cookie_access": {}, "local_fonts": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://pocketoption.com:443,*": {"last_modified": "13392904407441035", "setting": {"lastEngagementTime": 1.3392904407441024e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 6.6, "rawScore": 6.6}}}, "sound": {}, "ssl_cert_decisions": {}, "storage_access": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_storage_access": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "121.0.6167.85", "creation_time": "*****************", "exit_type": "Crashed", "last_engagement_time": "*****************", "last_time_obsolete_http_credentials_removed": **********.595255, "last_time_password_store_metrics_reported": **********.235075, "managed": {"banner_state": 2}, "managed_user_id": "", "name": "Person 1", "password_account_storage_settings": {}, "were_old_google_logins_removed": true}, "protection": {"macs": {"browser": {"show_home_button": "3E2D720C69D4CF848E742FF0B86F02FC75E1829370A4BEEE80F9791CD712703D"}, "default_search_provider_data": {"template_url_data": "57F888D655155FEDD6606778160329FEB8B6C68489A49A015AC87DA859DBAA08"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "5339ABA41B14426CC4F8198E9E7EC253462E9AE04176B4BC4C018B8EC0EFBA9A", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "78E9486F90EBADE48B34447048DDD5219EE64CEF54677C162863066C31A6F1B5"}}, "google": {"services": {"account_id": "3389B3CB2F3992E4342EE2A0289D3AC90A0CB845EF43B94E7E8638B6E472CC8A", "last_account_id": "5A14C40EB949EC04D15FD05636E42AFFF00CE3CB2D270BE8495739AE5DCDE993", "last_username": "DC38C915B632D33FF090FE057B25315C3D8A06DB317B501E86F05A75C88099FA"}}, "homepage": "1827042C1FD1FB2D4253208CF404981C90F1BE69DC5E1AB81914FFD1F179F005", "homepage_is_newtabpage": "120AFE5B423652918260147323340CDAA0B24E7077E0B815CF76E1FE796EC84E", "media": {"cdm": {"origin_data": "C36CBC1D3C49F90AC4F46AE10AB7778A730570A6DBB2A27161E3317C329147F3"}, "storage_id_salt": "6EE170A4A57194D8484E1FD397F2B0FFD3E5355890AB1627F67C211A5C1D4FEE"}, "pinned_tabs": "2D18703F174FF89BAFBAB85CFE0B3855A123CA9B0659DA79A38A8AB177481D98", "prefs": {"preference_reset_time": "047D726F06B2FE1575917E54FC06F4F9E65D3E26F6409A99E4281B1EA489ABBD"}, "safebrowsing": {"incidents_sent": "B4653268BA5CED8E2F31B3455DABF754FBDB2EC416B80D3DD1BFE259D0F306D7"}, "search_provider_overrides": "CCBAD63574981F49B00BD72A9146B96FC6AEF1CB0CC915503051CF16A0275755", "session": {"restore_on_startup": "5C43341D1EF79B2252EB1A36CFA85950BFDC5128BFD3482E9D083E2CAB989812", "startup_urls": "F34DC22B9B121DFEF13F23B8B744F1C7E706EA91AC24288D553373A35CC7289B"}}}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13392900564"}, "segmentation_platform": {"last_db_compaction_time": "13392777599000000"}, "sessions": {"event_log": [{"crashed": false, "time": "13392900564186420", "type": 0}, {"crashed": true, "time": "13392900596550851", "type": 0}, {"crashed": true, "time": "13392904400869621", "type": 0}], "session_data_status": 1}, "settings": {"a11y": {"apply_page_colors_only_on_increased_contrast": true}}, "signin": {"allowed": true}, "spellcheck": {"dictionaries": ["en-US"]}, "supervised_user": {"metrics": {"day_id": 155009}}, "sync": {"autofill_wallet_import_enabled_migrated": true}, "tracking_protection": {"tracking_protection_3pcd_enabled": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"daily_metrics": {"https://pocketoption.com/en/cabinet?source=pwa": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 3, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}}, "daily_metrics_date": "13392835200000000", "did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "error_loaded_policy_apps_migrated": true, "last_preinstall_synchronize_version": "121"}}