{"name": "pocket-option-trading-bot", "version": "1.0.0", "description": "Automated trading bot for Pocket Option platform", "main": "src/bot.js", "scripts": {"start": "node src/bot.js", "dev": "nodemon src/bot.js", "test": "jest", "session:info": "node src/session-cli.js info", "session:clear": "node src/session-cli.js clear", "session:export": "node src/session-cli.js export", "session:import": "node src/session-cli.js import"}, "keywords": ["trading", "bot", "pocket-option", "websocket", "automation"], "author": "Trading Bot Developer", "license": "MIT", "dependencies": {"ws": "^8.14.2", "puppeteer": "^21.5.2", "axios": "^1.6.2", "dotenv": "^16.3.1", "winston": "^3.11.0", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}