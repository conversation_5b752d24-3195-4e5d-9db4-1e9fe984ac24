{"version": 3, "file": "css/vendors.daterangepicker.min.css", "mappings": "AAmHA,iBAGE,qBAhG2C,CAiG3C,iBAnE+B,CAiE/B,aAhG2C,CAyG3C,UAJA,cAxE+B,CAuE/B,WA1E+B,CAqE/B,kBASA,UALA,WAMA,CAKA,+CAIE,mCACA,WAHA,qBADA,iBAIA,CAGF,wBAKE,6BADA,kCADA,mCAFA,QAIA,CAGF,uBAIE,6BACA,kCAFA,mCAFA,QAIA,CAIA,kCAEE,UAGF,iCAEE,WAaF,uEACE,OAGA,iBACA,kBAHA,QACA,OAEA,CAKF,mCAEE,SAGF,kCAEE,UAIJ,wBACE,gBAGA,+BAGE,sBACA,0BAFA,YADA,QAGA,CAGF,8BAGE,sBACA,0BAFA,YADA,QAGA,CAIJ,+BACE,eACA,YA/I+B,CAmJ/B,kEACE,WAMF,yCACE,cAIJ,2BACE,aAEA,WADA,eA7L6B,CAiM3B,kDACE,YAIJ,4DAKE,eAHA,kBADA,kBAIA,CAIJ,iCAIE,sBAHA,sBAEA,iBA7M6B,CA4M7B,WA1OyC,CA+O3C,uBAEE,QAAO,CADP,UACA,CAGF,wCAKE,6BADA,iBA1N6B,CA6N7B,eAJA,WA3MkC,CAyMlC,kBAKA,mBAJA,UAKA,CAGE,wEACE,qBAvPqC,CAwPrC,wBA7PqC,CA8PrC,aAlQqC,CAsQzC,kDAEE,WADA,aACA,CAMA,6HACE,qBA1PqC,CA2PrC,wBA5PqC,CA6PrC,UA9PqC,CAoQzC,6BACE,wBA3QuC,CA4QvC,wBA7QuC,CAiRvC,eAAc,CAHd,UAGA,CAGF,+BACE,0BAGF,6BACE,0BAGF,wCACE,iBA1Q2B,CA8Q3B,4DACE,wBA9RqC,CA+RrC,wBA9RqC,CA+RrC,UAjSqC,CAuSzC,0BACE,WAQF,8DACE,WACA,mBACA,6BAKF,uEAKE,eAJA,eAEA,YACA,SAFA,WAGA,CAGF,oCACE,gBACA,UAGF,mCACE,UAGF,gJAEE,eAAc,CADd,UACA,CAOJ,6BACE,sBACA,iBAnS2C,CAoS3C,UAxS2C,CA2S3C,cAFA,WA3S2C,CA4S3C,gBA5S2C,CAiT3C,eACA,qBAJA,sBAKA,WAEA,oCACE,sBACA,iBAjTyC,CAqT7C,wCACE,kBAEA,0CAIE,SAHA,kBAIA,QAIF,iCAEE,iBADA,kBACA,CAEF,8CACE,UACA,UAOJ,gCAGE,gBAvV2C,CAsV3C,gBAGA,kBADA,kBAHA,iBAIA,CAGE,gDACE,UAjVuC,CAkVvC,mBAUR,QAEE,WADA,eAEA,WACA,gBAEA,WACE,gBACA,cACA,UACA,WAGF,WAEE,wBAtW0C,CAuW1C,yBACA,iBApZ6B,CAqZ7B,UA1W0C,CA6W1C,eAPA,eAMA,kBADA,gBAEA,CAQA,mCACE,qBAtXwC,CAuXxC,sBACA,UAjXwC,CAuX9C,wBACE,iBACE,WAGE,4BACE,YAMA,mCACE,WAIJ,uCACE,WAIA,0EACE,UAAU,CAIZ,0EACE,WAAW,CAKjB,qBACE,cACA,gBAEE,oCACE,WACA,eAEA,oDAGE,4BAA2B,CAF3B,kBACA,yBACA,CAIJ,qCACE,cAEA,qDAGE,2BAA0B,CAF1B,iBACA,wBACA,CASN,sGACE,mBAGF,4DACE,WAGJ,qBACE,cACA,iBAEE,oCACE,YACA,cAEA,oDAGE,2BAA0B,CAF1B,iBACA,wBACA,CAIJ,qCACE,eAEA,qDAGE,4BAA2B,CAF3B,kBACA,yBACA,CASN,sGACE,kBAGF,4DAEE,YADA,gBACA,EAMR,wBAEI,yBACE,WAGA,6BACE,WAIF,6BACE,YAIJ,gCACE", "sources": ["webpack:///../../platform/libraries/daterangepicker/daterangepicker.scss"], "sourcesContent": ["//\r\n// A stylesheet for use with Bootstrap 3.x\r\n// @author: <PERSON> http://www.dangrossman.info/\r\n// @copyright: Copyright (c) 2012-2015 <PERSON>. All rights reserved.\r\n// @license: Licensed under the MIT license. See http://www.opensource.org/licenses/mit-license.php\r\n// @website: https://www.improvely.com/\r\n//\r\n\r\n//\r\n// VARIABLES\r\n//\r\n\r\n//\r\n// Settings\r\n\r\n// The class name to contain everything within.\r\n$prefix-class: daterangepicker;\r\n$arrow-size:     7px !default;\r\n\r\n//\r\n// Colors\r\n$daterangepicker-color:                      inherit !default;\r\n$daterangepicker-bg-color:                   #fff !default;\r\n\r\n$daterangepicker-cell-color:                 $daterangepicker-color !default;\r\n$daterangepicker-cell-border-color:          transparent !default;\r\n$daterangepicker-cell-bg-color:              $daterangepicker-bg-color !default;\r\n\r\n$daterangepicker-cell-hover-color:           $daterangepicker-color !default;\r\n$daterangepicker-cell-hover-border-color:    $daterangepicker-cell-border-color !default;\r\n$daterangepicker-cell-hover-bg-color:        #eee !default;\r\n\r\n$daterangepicker-in-range-color:             #000 !default;\r\n$daterangepicker-in-range-border-color:      transparent !default;\r\n$daterangepicker-in-range-bg-color:          #ebf4f8 !default;\r\n\r\n$daterangepicker-active-color:               #fff !default;\r\n$daterangepicker-active-bg-color:            #357ebd !default;\r\n$daterangepicker-active-border-color:        transparent !default;\r\n\r\n$daterangepicker-unselected-color:           #999 !default;\r\n$daterangepicker-unselected-border-color:    transparent !default;\r\n$daterangepicker-unselected-bg-color:        #fff !default;\r\n\r\n//\r\n// daterangepicker\r\n$daterangepicker-width:          278px !default;\r\n$daterangepicker-padding:        4px !default;\r\n$daterangepicker-z-index:        3000 !default;\r\n\r\n$daterangepicker-border-size:    1px !default;\r\n$daterangepicker-border-color:   #ccc !default;\r\n$daterangepicker-border-radius:  4px !default;\r\n\r\n\r\n//\r\n// Calendar\r\n$daterangepicker-calendar-margin:              $daterangepicker-padding !default;\r\n$daterangepicker-calendar-bg-color:            $daterangepicker-bg-color !default;\r\n\r\n$daterangepicker-calendar-border-size:         1px !default;\r\n$daterangepicker-calendar-border-color:        $daterangepicker-bg-color !default;\r\n$daterangepicker-calendar-border-radius:       $daterangepicker-border-radius !default;\r\n\r\n//\r\n// Calendar Cells\r\n$daterangepicker-cell-size:           20px !default;\r\n$daterangepicker-cell-width:          $daterangepicker-cell-size !default;\r\n$daterangepicker-cell-height:         $daterangepicker-cell-size !default;\r\n\r\n$daterangepicker-cell-border-radius:  $daterangepicker-calendar-border-radius !default;\r\n$daterangepicker-cell-border-size:    1px !default;\r\n\r\n//\r\n// Dropdowns\r\n$daterangepicker-dropdown-z-index: $daterangepicker-z-index + 1 !default;\r\n\r\n//\r\n// Controls\r\n$daterangepicker-control-height:               30px !default;\r\n$daterangepicker-control-line-height:          $daterangepicker-control-height !default;\r\n$daterangepicker-control-color:                #555 !default;\r\n\r\n$daterangepicker-control-border-size:          1px !default;\r\n$daterangepicker-control-border-color:         #ccc !default;\r\n$daterangepicker-control-border-radius:        4px !default;\r\n\r\n$daterangepicker-control-active-border-size:   1px !default;\r\n$daterangepicker-control-active-border-color:  #08c !default;\r\n$daterangepicker-control-active-border-radius: $daterangepicker-control-border-radius !default;\r\n\r\n$daterangepicker-control-disabled-color:       #ccc !default;\r\n\r\n//\r\n// Ranges\r\n$daterangepicker-ranges-color:                #08c !default;\r\n$daterangepicker-ranges-bg-color:             #f5f5f5 !default;\r\n\r\n$daterangepicker-ranges-border-size:          1px !default;\r\n$daterangepicker-ranges-border-color:         $daterangepicker-ranges-bg-color !default;\r\n$daterangepicker-ranges-border-radius:        $daterangepicker-border-radius !default;\r\n\r\n$daterangepicker-ranges-hover-color:          #fff !default;\r\n$daterangepicker-ranges-hover-bg-color:       $daterangepicker-ranges-color !default;\r\n$daterangepicker-ranges-hover-border-size:    $daterangepicker-ranges-border-size !default;\r\n$daterangepicker-ranges-hover-border-color:   $daterangepicker-ranges-hover-bg-color !default;\r\n$daterangepicker-ranges-hover-border-radius:  $daterangepicker-border-radius !default;\r\n\r\n$daterangepicker-ranges-active-border-size:   $daterangepicker-ranges-border-size !default;\r\n$daterangepicker-ranges-active-border-color:  $daterangepicker-ranges-bg-color !default;\r\n$daterangepicker-ranges-active-border-radius: $daterangepicker-border-radius !default;\r\n\r\n//\r\n// STYLESHEETS\r\n//\r\n.#{$prefix-class} {\r\n  position: absolute;\r\n  color: $daterangepicker-color;\r\n  background-color: $daterangepicker-bg-color;\r\n  border-radius: $daterangepicker-border-radius;\r\n  width: $daterangepicker-width;\r\n  padding: $daterangepicker-padding;\r\n  margin-top: $daterangepicker-border-size;\r\n\r\n  // TODO: Should these be parameterized??\r\n  top: 100px;\r\n  left: 20px;\r\n\r\n  $arrow-prefix-size: $arrow-size;\r\n  $arrow-suffix-size: ($arrow-size - $daterangepicker-border-size);\r\n\r\n  &:before, &:after {\r\n    position: absolute;\r\n    display: inline-block;\r\n\r\n    border-bottom-color: rgba(0, 0, 0, 0.2);\r\n    content: '';\r\n  }\r\n\r\n  &:before {\r\n    top: -$arrow-prefix-size;\r\n\r\n    border-right: $arrow-prefix-size solid transparent;\r\n    border-left: $arrow-prefix-size solid transparent;\r\n    border-bottom: $arrow-prefix-size solid $daterangepicker-border-color;\r\n  }\r\n\r\n  &:after {\r\n    top: -$arrow-suffix-size;\r\n\r\n    border-right: $arrow-suffix-size solid transparent;\r\n    border-bottom: $arrow-suffix-size solid $daterangepicker-bg-color;\r\n    border-left: $arrow-suffix-size solid transparent;\r\n  }\r\n\r\n  &.opensleft {\r\n    &:before {\r\n      // TODO: Make this relative to prefix size.\r\n      right: $arrow-prefix-size + 2px;\r\n    }\r\n\r\n    &:after {\r\n      // TODO: Make this relative to suffix size.\r\n      right: $arrow-suffix-size + 4px;\r\n    }\r\n  }\r\n\r\n  &.openscenter {\r\n    &:before {\r\n      left: 0;\r\n      right: 0;\r\n      width: 0;\r\n      margin-left: auto;\r\n      margin-right: auto;\r\n    }\r\n\r\n    &:after {\r\n      left: 0;\r\n      right: 0;\r\n      width: 0;\r\n      margin-left: auto;\r\n      margin-right: auto;\r\n    }\r\n  }\r\n\r\n  &.opensright {\r\n    &:before {\r\n      // TODO: Make this relative to prefix size.\r\n      left: $arrow-prefix-size + 2px;\r\n    }\r\n\r\n    &:after {\r\n      // TODO: Make this relative to suffix size.\r\n      left: $arrow-suffix-size + 4px;\r\n    }\r\n  }\r\n\r\n  &.dropup {\r\n    margin-top: -5px;\r\n\r\n    // NOTE: Note sure why these are special-cased.\r\n    &:before {\r\n      top: initial;\r\n      bottom: -$arrow-prefix-size;\r\n      border-bottom: initial;\r\n      border-top: $arrow-prefix-size solid $daterangepicker-border-color;\r\n    }\r\n\r\n    &:after {\r\n      top: initial;\r\n      bottom:-$arrow-suffix-size;\r\n      border-bottom: initial;\r\n      border-top: $arrow-suffix-size solid $daterangepicker-bg-color;\r\n    }\r\n  }\r\n\r\n  &.dropdown-menu {\r\n    max-width: none;\r\n    z-index: $daterangepicker-dropdown-z-index;\r\n  }\r\n\r\n  &.single {\r\n    .ranges, .calendar {\r\n      float: none;\r\n    }\r\n  }\r\n\r\n  /* Calendars */\r\n  &.show-calendar {\r\n    .calendar {\r\n      display: block;\r\n    }\r\n  }\r\n\r\n  .calendar {\r\n    display: none;\r\n    max-width: $daterangepicker-width - ($daterangepicker-calendar-margin * 2);\r\n    margin: $daterangepicker-calendar-margin;\r\n\r\n    &.single {\r\n      .calendar-table {\r\n        border: none;\r\n      }\r\n    }\r\n\r\n    th, td {\r\n      white-space: nowrap;\r\n      text-align: center;\r\n\r\n      // TODO: Should this actually be hard-coded?\r\n      min-width: 32px;\r\n    }\r\n  }\r\n\r\n  .calendar-table {\r\n    border: $daterangepicker-calendar-border-size solid $daterangepicker-calendar-border-color;\r\n    padding: $daterangepicker-calendar-margin;\r\n    border-radius: $daterangepicker-calendar-border-radius;\r\n    background-color: $daterangepicker-calendar-bg-color;\r\n  }\r\n\r\n  table {\r\n    width: 100%;\r\n    margin: 0;\r\n  }\r\n\r\n  td, th {\r\n    text-align: center;\r\n    width: $daterangepicker-cell-width;\r\n    height: $daterangepicker-cell-height;\r\n    border-radius: $daterangepicker-cell-border-radius;\r\n    border: $daterangepicker-cell-border-size solid $daterangepicker-cell-border-color;\r\n    white-space: nowrap;\r\n    cursor: pointer;\r\n\r\n    &.available {\r\n      &:hover {\r\n        background-color: $daterangepicker-cell-hover-bg-color;\r\n        border-color: $daterangepicker-cell-hover-border-color;\r\n        color: $daterangepicker-cell-hover-color;\r\n      }\r\n    }\r\n\r\n    &.week {\r\n      font-size: 80%;\r\n      color: #ccc;\r\n    }\r\n  }\r\n\r\n  td {\r\n    &.off {\r\n      &, &.in-range, &.start-date, &.end-date {\r\n        background-color: $daterangepicker-unselected-bg-color;\r\n        border-color: $daterangepicker-unselected-border-color;\r\n        color: $daterangepicker-unselected-color;\r\n      }\r\n    }\r\n\r\n    //\r\n    // Date Range\r\n    &.in-range {\r\n      background-color: $daterangepicker-in-range-bg-color;\r\n      border-color: $daterangepicker-in-range-border-color;\r\n      color: $daterangepicker-in-range-color;\r\n\r\n      // TODO: Should this be static or should it be parameterized?\r\n      border-radius: 0;\r\n    }\r\n\r\n    &.start-date {\r\n      border-radius: $daterangepicker-cell-border-radius 0 0 $daterangepicker-cell-border-radius;\r\n    }\r\n\r\n    &.end-date {\r\n      border-radius: 0 $daterangepicker-cell-border-radius $daterangepicker-cell-border-radius 0;\r\n    }\r\n\r\n    &.start-date.end-date {\r\n      border-radius: $daterangepicker-cell-border-radius;\r\n    }\r\n\r\n    &.active {\r\n      &, &:hover {\r\n        background-color: $daterangepicker-active-bg-color;\r\n        border-color: $daterangepicker-active-border-color;\r\n        color: $daterangepicker-active-color;\r\n      }\r\n    }\r\n  }\r\n\r\n  th {\r\n    &.month {\r\n      width: auto;\r\n    }\r\n  }\r\n\r\n  //\r\n  // Disabled Controls\r\n  //\r\n  td, option {\r\n    &.disabled {\r\n      color: #999;\r\n      cursor: not-allowed;\r\n      text-decoration: line-through;\r\n    }\r\n  }\r\n\r\n  select {\r\n    &.monthselect, &.yearselect {\r\n      font-size: 12px;\r\n      padding: 1px;\r\n      height: auto;\r\n      margin: 0;\r\n      cursor: default;\r\n    }\r\n\r\n    &.monthselect {\r\n      margin-right: 2%;\r\n      width: 56%;\r\n    }\r\n\r\n    &.yearselect {\r\n      width: 40%;\r\n    }\r\n\r\n    &.hourselect, &.minuteselect, &.secondselect, &.ampmselect {\r\n      width: 50px;\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n\r\n  //\r\n  // Text Input Controls (above calendar)\r\n  //\r\n  .input-mini {\r\n    border: $daterangepicker-control-border-size solid $daterangepicker-control-border-color;\r\n    border-radius: $daterangepicker-control-border-radius;\r\n    color: $daterangepicker-control-color;\r\n    height: $daterangepicker-control-line-height;\r\n    line-height: $daterangepicker-control-height;\r\n    display: block;\r\n    vertical-align: middle;\r\n\r\n    // TODO: Should these all be static, too??\r\n    margin: 0 0 5px 0;\r\n    padding: 0 6px 0 28px;\r\n    width: 100%;\r\n\r\n    &.active {\r\n      border: $daterangepicker-control-active-border-size solid $daterangepicker-control-active-border-color;\r\n      border-radius: $daterangepicker-control-active-border-radius;\r\n    }\r\n  }\r\n\r\n  .daterangepicker_input {\r\n    position: relative;\r\n\r\n    i {\r\n      position: absolute;\r\n\r\n      // NOTE: These appear to be eyeballed to me...\r\n      left: 8px;\r\n      top: 8px;\r\n    }\r\n  }\r\n  &.rtl {\r\n    .input-mini {\r\n      padding-right: 28px;\r\n      padding-left: 6px;\r\n    }\r\n    .daterangepicker_input i {\r\n      left: auto;\r\n      right: 8px;\r\n    }\r\n  }\r\n\r\n  //\r\n  // Time Picker\r\n  //\r\n  .calendar-time {\r\n    text-align: center;\r\n    margin: 5px auto;\r\n    line-height: $daterangepicker-control-line-height;\r\n    position: relative;\r\n    padding-left: 28px;\r\n\r\n    select {\r\n      &.disabled {\r\n        color: $daterangepicker-control-disabled-color;\r\n        cursor: not-allowed;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n//\r\n// Predefined Ranges\r\n//\r\n\r\n.ranges {\r\n  font-size: 11px;\r\n  float: none;\r\n  margin: 4px;\r\n  text-align: left;\r\n\r\n  ul {\r\n    list-style: none;\r\n    margin: 0 auto;\r\n    padding: 0;\r\n    width: 100%;\r\n  }\r\n\r\n  li {\r\n    font-size: 13px;\r\n    background-color: $daterangepicker-ranges-bg-color;\r\n    border: $daterangepicker-ranges-border-size solid $daterangepicker-ranges-border-color;\r\n    border-radius: $daterangepicker-ranges-border-radius;\r\n    color: $daterangepicker-ranges-color;\r\n    padding: 3px 12px;\r\n    margin-bottom: 8px;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      background-color: $daterangepicker-ranges-hover-bg-color;\r\n      border: $daterangepicker-ranges-hover-border-size solid $daterangepicker-ranges-hover-border-color;\r\n      color: $daterangepicker-ranges-hover-color;\r\n    }\r\n\r\n    &.active {\r\n      background-color: $daterangepicker-ranges-hover-bg-color;\r\n      border: $daterangepicker-ranges-hover-border-size solid $daterangepicker-ranges-hover-border-color;\r\n      color: $daterangepicker-ranges-hover-color;\r\n    }\r\n  }\r\n}\r\n\r\n/*  Larger Screen Styling */\r\n@media (min-width: 564px) {\r\n  .#{$prefix-class} {\r\n    width: auto;\r\n\r\n    .ranges {\r\n      ul {\r\n        width: 160px;\r\n      }\r\n    }\r\n\r\n    &.single {\r\n      .ranges {\r\n        ul {\r\n          width: 100%;\r\n        }\r\n      }\r\n\r\n      .calendar.left {\r\n        clear: none;\r\n      }\r\n\r\n      &.ltr {\r\n        .ranges, .calendar {\r\n          float:left;\r\n        }\r\n      }\r\n      &.rtl {\r\n        .ranges, .calendar {\r\n          float:right;\r\n        }\r\n      }\r\n    }\r\n\r\n    &.ltr {\r\n      direction: ltr;\r\n      text-align: left;\r\n      .calendar{\r\n        &.left {\r\n          clear: left;\r\n          margin-right: 0;\r\n\r\n          .calendar-table {\r\n            border-right: none;\r\n            border-top-right-radius: 0;\r\n            border-bottom-right-radius: 0;\r\n          }\r\n        }\r\n\r\n        &.right {\r\n          margin-left: 0;\r\n\r\n          .calendar-table {\r\n            border-left: none;\r\n            border-top-left-radius: 0;\r\n            border-bottom-left-radius: 0;\r\n          }\r\n        }\r\n      }\r\n\r\n      .left .daterangepicker_input {\r\n        padding-right: 12px;\r\n      }\r\n\r\n      .calendar.left .calendar-table {\r\n        padding-right: 12px;\r\n      }\r\n\r\n      .ranges, .calendar {\r\n        float: left;\r\n      }\r\n    }\r\n    &.rtl {\r\n      direction: rtl;\r\n      text-align: right;\r\n      .calendar{\r\n        &.left {\r\n          clear: right;\r\n          margin-left: 0;\r\n\r\n          .calendar-table {\r\n            border-left: none;\r\n            border-top-left-radius: 0;\r\n            border-bottom-left-radius: 0;\r\n          }\r\n        }\r\n\r\n        &.right {\r\n          margin-right: 0;\r\n\r\n          .calendar-table {\r\n            border-right: none;\r\n            border-top-right-radius: 0;\r\n            border-bottom-right-radius: 0;\r\n          }\r\n        }\r\n      }\r\n\r\n      .left .daterangepicker_input {\r\n        padding-left: 12px;\r\n      }\r\n\r\n      .calendar.left .calendar-table {\r\n        padding-left: 12px;\r\n      }\r\n\r\n      .ranges, .calendar {\r\n        text-align: right;\r\n        float: right;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (min-width: 730px) {\r\n  .#{$prefix-class} {\r\n    .ranges {\r\n      width: auto;\r\n    }\r\n    &.ltr {\r\n      .ranges {\r\n        float: left;\r\n      }\r\n    }\r\n    &.rtl {\r\n      .ranges {\r\n        float: right;\r\n      }\r\n    }\r\n\r\n    .calendar.left {\r\n      clear: none !important;\r\n    }\r\n  }\r\n}\r\n"], "names": [], "sourceRoot": ""}