const PocketOptionWebSocket = require('./websocket-client')
const DOMController = require('./dom-controller')
const TradingStrategy = require('./trading-strategy')
const logger = require('./utils/logger')
const config = require('./config')

class PocketOptionBot {
	constructor() {
		this.wsClient = new PocketOptionWebSocket()
		this.domController = new DOMController()
		this.strategy = new TradingStrategy()
		this.isRunning = false
		this.activeTrades = new Map()
		this.balance = 0
		this.startTime = Date.now()
	}

	async initialize() {
		try {
			logger.info('Initializing Pocket Option Trading Bot')

			// Initialize browser and DOM controller
			const browserInit = await this.domController.initialize()
			if (!browserInit) {
				throw new Error('Failed to initialize browser')
			}

			// Inject WebSocket monitor
			await this.domController.injectWebSocketMonitor()

			// Handle login with session persistence
			const loginSuccess = await this.domController.handleLogin()
			if (!loginSuccess) {
				throw new Error('Failed to login to Pocket Option')
			}

			// Switch to demo account
			await this.domController.switchToDemo()

			// Set up initial trading parameters
			await this.setupTradingParameters()

			// Wait a bit for the page to fully load and discover WebSocket URL
			await new Promise(resolve => setTimeout(resolve, 5000))

			// Try to get WebSocket URL from the page
			const wsUrl = await this.domController.getWebSocketUrl()
			if (wsUrl) {
				logger.info('WebSocket URL discovered', { url: wsUrl })
				await this.initializeWebSocket(wsUrl)
			} else {
				logger.warn('WebSocket URL not found, using default')
				await this.initializeWebSocket(config.pocketOption.websocketUrl)
			}

			// Set up event listeners
			this.setupEventListeners()

			logger.info('Bot initialization completed successfully')
			return true
		} catch (error) {
			logger.error('Failed to initialize bot', error)
			return false
		}
	}

	async setupTradingParameters() {
		try {
			// Select default asset
			await this.domController.selectAsset(config.trading.defaultAsset)

			// Select default timeframe
			await this.domController.selectTimeframe(config.trading.defaultTimeframe)

			// Set default trade amount
			await this.domController.setTradeAmount(config.trading.defaultAmount)

			logger.info('Trading parameters set up', {
				asset: config.trading.defaultAsset,
				timeframe: config.trading.defaultTimeframe,
				amount: config.trading.defaultAmount
			})
		} catch (error) {
			logger.error('Failed to setup trading parameters', error)
		}
	}

	async initializeWebSocket(wsUrl) {
		try {
			await this.wsClient.connect(wsUrl)

			// Subscribe to market data for default asset
			this.wsClient.subscribe(config.trading.defaultAsset)

			logger.info('WebSocket initialized and subscribed to market data')
		} catch (error) {
			logger.error('Failed to initialize WebSocket', error)
		}
	}

	setupEventListeners() {
		// WebSocket events
		this.wsClient.on('connected', () => {
			logger.info('WebSocket connected')
		})

		this.wsClient.on('disconnected', () => {
			logger.warn('WebSocket disconnected')
		})

		this.wsClient.on('priceUpdate', data => {
			this.handlePriceUpdate(data)
		})

		this.wsClient.on('tradeResult', data => {
			this.handleTradeResult(data)
		})

		this.wsClient.on('balanceUpdate', data => {
			this.handleBalanceUpdate(data)
		})

		// Process termination handlers
		process.on('SIGINT', () => this.shutdown())
		process.on('SIGTERM', () => this.shutdown())
	}

	async handlePriceUpdate(data) {
		try {
			const { asset, price, timestamp } = data

			// Analyze market and get trading signal
			const signal = this.strategy.analyzeMarket(asset, { price, timestamp })

			if (signal && this.isRunning) {
				await this.executeTradeSignal(signal)
			}
		} catch (error) {
			logger.error('Error handling price update', error)
		}
	}

	async executeTradeSignal(signal) {
		try {
			// Check if we can execute the trade
			if (!this.canExecuteTrade()) {
				return
			}

			// Calculate trade amount based on risk management
			const tradeAmount = this.calculateTradeAmount()

			// Set trade amount
			await this.domController.setTradeAmount(tradeAmount)

			// Execute the trade
			const success = await this.domController.executeTrade(signal.direction)

			if (success) {
				const tradeId = `trade_${Date.now()}`
				this.activeTrades.set(tradeId, {
					id: tradeId,
					asset: signal.asset,
					direction: signal.direction,
					amount: tradeAmount,
					confidence: signal.confidence,
					timestamp: Date.now(),
					reasons: signal.reasons
				})

				logger.trade('Trade executed successfully', {
					tradeId,
					direction: signal.direction,
					amount: tradeAmount,
					asset: signal.asset,
					confidence: signal.confidence
				})
			}
		} catch (error) {
			logger.error('Error executing trade signal', error)
		}
	}

	canExecuteTrade() {
		// Check if bot is running
		if (!this.isRunning) return false

		// Check maximum concurrent trades
		if (this.activeTrades.size >= config.trading.maxConcurrentTrades) {
			return false
		}

		// Check balance
		if (this.balance < config.trading.minAmount) {
			logger.warn('Insufficient balance for trading')
			return false
		}

		return true
	}

	calculateTradeAmount() {
		// Simple risk management: use percentage of balance
		const riskAmount = this.balance * (config.trading.riskPercentage / 100)

		// Ensure amount is within limits
		let amount = Math.max(config.trading.minAmount, riskAmount)
		amount = Math.min(config.trading.maxAmount, amount)

		return Math.round(amount * 100) / 100 // Round to 2 decimal places
	}

	async handleTradeResult(data) {
		try {
			const { tradeId, result, profit } = data

			if (this.activeTrades.has(tradeId)) {
				const trade = this.activeTrades.get(tradeId)

				// Record result in strategy
				this.strategy.recordTradeResult(result, profit)

				// Remove from active trades
				this.activeTrades.delete(tradeId)

				logger.trade('Trade completed', {
					tradeId,
					result,
					profit,
					asset: trade.asset,
					direction: trade.direction,
					amount: trade.amount
				})
			}
		} catch (error) {
			logger.error('Error handling trade result', error)
		}
	}

	async handleBalanceUpdate(data) {
		try {
			this.balance = data.balance
			logger.info('Balance updated', { balance: this.balance, currency: data.currency })
		} catch (error) {
			logger.error('Error handling balance update', error)
		}
	}

	async start() {
		try {
			if (this.isRunning) {
				logger.warn('Bot is already running')
				return
			}

			logger.info('Starting trading bot')
			this.isRunning = true

			// Get initial balance
			this.balance = (await this.domController.getBalance()) || 0

			// Start monitoring loop
			this.startMonitoringLoop()

			logger.info('Trading bot started successfully', { balance: this.balance })
		} catch (error) {
			logger.error('Failed to start trading bot', error)
		}
	}

	async stop() {
		try {
			logger.info('Stopping trading bot')
			this.isRunning = false

			// Wait for active trades to complete or timeout
			const timeout = 60000 // 1 minute
			const startTime = Date.now()

			while (this.activeTrades.size > 0 && Date.now() - startTime < timeout) {
				await new Promise(resolve => setTimeout(resolve, 1000))
			}

			logger.info('Trading bot stopped', {
				activeTrades: this.activeTrades.size,
				runtime: Date.now() - this.startTime
			})
		} catch (error) {
			logger.error('Error stopping trading bot', error)
		}
	}

	startMonitoringLoop() {
		setInterval(async () => {
			if (!this.isRunning) return

			try {
				// Update balance
				const currentBalance = await this.domController.getBalance()
				if (currentBalance !== null) {
					this.balance = currentBalance
				}

				// Check for completed trades
				const openTrades = await this.domController.getOpenTrades()
				this.updateActiveTrades(openTrades)

				// Log status
				logger.info('Bot status', {
					isRunning: this.isRunning,
					balance: this.balance,
					activeTrades: this.activeTrades.size,
					runtime: Date.now() - this.startTime
				})
			} catch (error) {
				logger.error('Error in monitoring loop', error)
			}
		}, 10000) // Every 10 seconds
	}

	updateActiveTrades(openTrades) {
		// Compare with our active trades and update status
		// This is a simplified implementation
		const openTradeIds = new Set(openTrades.map(t => t.id))

		for (const [tradeId, trade] of this.activeTrades) {
			if (!openTradeIds.has(tradeId)) {
				// Trade is no longer open, assume it completed
				this.activeTrades.delete(tradeId)
				logger.trade('Trade completed (detected via monitoring)', { tradeId })
			}
		}
	}

	async shutdown() {
		try {
			logger.info('Shutting down bot')

			await this.stop()

			// Disconnect WebSocket
			this.wsClient.disconnect()

			// Close browser
			await this.domController.close()

			logger.info('Bot shutdown completed')
			process.exit(0)
		} catch (error) {
			logger.error('Error during shutdown', error)
			process.exit(1)
		}
	}

	// Utility methods
	getStatus() {
		return {
			isRunning: this.isRunning,
			balance: this.balance,
			activeTrades: this.activeTrades.size,
			runtime: Date.now() - this.startTime,
			wsConnected: this.wsClient.isConnected
		}
	}

	async takeScreenshot() {
		const filename = `bot_screenshot_${Date.now()}.png`
		return await this.domController.takeScreenshot(filename)
	}
}

// Main execution
async function main() {
	const bot = new PocketOptionBot()

	try {
		const initialized = await bot.initialize()
		if (initialized) {
			await bot.start()

			// Keep the process running
			process.stdin.resume()
		} else {
			logger.error('Failed to initialize bot')
			process.exit(1)
		}
	} catch (error) {
		logger.error('Fatal error in main', error)
		process.exit(1)
	}
}

// Run the bot if this file is executed directly
if (require.main === module) {
	main()
}

module.exports = PocketOptionBot
