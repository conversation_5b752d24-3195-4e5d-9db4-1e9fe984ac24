{"level":"info","message":"Initializing Pocket Option Trading Bot","timestamp":"2025-05-28 18:02:00"}
{"level":"info","message":"Initializing browser","timestamp":"2025-05-28 18:02:00"}
{"level":"info","message":"Browser initialized successfully","timestamp":"2025-05-28 18:02:02"}
{"level":"info","message":"WebSocket monitor injected","timestamp":"2025-05-28 18:02:03"}
{"level":"info","message":"Navigating to trading page","timestamp":"2025-05-28 18:02:03"}
{"level":"error","message":"Failed to navigate to trading page","stack":"TimeoutError: Waiting for selector `input[type=\"text\"]` failed: Waiting failed: 10000ms exceeded\n    at new WaitTask (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\WaitTask.js:50:34)\n    at IsolatedWorld.waitForFunction (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Realm.js:25:26)\n    at PQueryHandler.waitFor (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\QueryHandler.js:170:95)\n    at runNextTicks (node:internal/process/task_queues:60:5)\n    at listOnTimeout (node:internal/timers:545:9)\n    at process.processTimers (node:internal/timers:519:7)\n    at async CdpFrame.waitForSelector (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Frame.js:494:21)\n    at async CdpPage.waitForSelector (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Page.js:1351:20)\n    at async DOMController.navigateToTradingPage (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:62:7)\n    at async PocketOptionBot.initialize (E:\\Code\\pocket-bot-v2\\src\\bot.js:32:26)","timestamp":"2025-05-28 18:02:17"}
{"level":"info","message":"Initializing Pocket Option Trading Bot","timestamp":"2025-05-28 18:09:24"}
{"level":"info","message":"Initializing browser with session persistence","timestamp":"2025-05-28 18:09:24"}
{"level":"info","message":"Created browser data directory","timestamp":"2025-05-28 18:09:24","userDataDir":"E:\\Code\\pocket-bot-v2\\browser-data"}
{"level":"info","message":"Session persistence enabled","timestamp":"2025-05-28 18:09:24","userDataDir":"E:\\Code\\pocket-bot-v2\\browser-data"}
{"level":"info","message":"Browser initialized successfully","timestamp":"2025-05-28 18:09:25"}
{"level":"info","message":"WebSocket monitor injected","timestamp":"2025-05-28 18:09:25"}
{"level":"info","message":"Handling login process","timestamp":"2025-05-28 18:09:25"}
{"level":"info","message":"No saved session found","timestamp":"2025-05-28 18:09:25"}
{"level":"info","message":"Navigating to trading page","timestamp":"2025-05-28 18:09:25"}
{"level":"error","message":"Failed to navigate to trading page","stack":"TimeoutError: Waiting for selector `input[type=\"text\"]` failed: Waiting failed: 10000ms exceeded\n    at new WaitTask (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\WaitTask.js:50:34)\n    at IsolatedWorld.waitForFunction (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Realm.js:25:26)\n    at PQueryHandler.waitFor (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\QueryHandler.js:170:95)\n    at runNextTicks (node:internal/process/task_queues:60:5)\n    at listOnTimeout (node:internal/timers:545:9)\n    at process.processTimers (node:internal/timers:519:7)\n    at async CdpFrame.waitForSelector (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Frame.js:494:21)\n    at async CdpPage.waitForSelector (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Page.js:1351:20)\n    at async DOMController.navigateToTradingPage (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:91:4)\n    at async DOMController.handleLogin (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:522:5)","timestamp":"2025-05-28 18:09:39"}
{"level":"info","message":"On login page, user not logged in","timestamp":"2025-05-28 18:09:41"}
{"level":"info","message":"Please log in manually in the browser window","timestamp":"2025-05-28 18:09:41"}
{"level":"info","message":"The bot will wait for up to 5 minutes for you to complete login","timestamp":"2025-05-28 18:09:41"}
{"level":"info","message":"Waiting for user to log in manually...","timeout":"300s","timestamp":"2025-05-28 18:09:41"}
{"level":"info","message":"On login page, user not logged in","timestamp":"2025-05-28 18:09:43"}
{"level":"info","message":"On login page, user not logged in","timestamp":"2025-05-28 18:09:46"}
{"level":"warn","message":"Unable to determine login status","timestamp":"2025-05-28 18:09:51"}
{"indicator":"[data-user]","level":"info","message":"User appears to be logged in","timestamp":"2025-05-28 18:09:55"}
{"level":"info","message":"User successfully logged in","timestamp":"2025-05-28 18:09:55"}
{"cookieCount":22,"level":"info","localStorageKeys":18,"message":"Session saved successfully","sessionStorageKeys":8,"timestamp":"2025-05-28 18:09:55","url":"https://pocketoption.com/en/cabinet/"}
{"level":"info","message":"Login successful and session saved","timestamp":"2025-05-28 18:09:55"}
{"level":"info","message":"Switching to demo account","timestamp":"2025-05-28 18:09:55"}
{"level":"info","message":"Initializing Pocket Option Trading Bot","timestamp":"2025-05-28 18:09:56"}
{"level":"info","message":"Initializing browser with session persistence","timestamp":"2025-05-28 18:09:56"}
{"level":"info","message":"Session persistence enabled","timestamp":"2025-05-28 18:09:56","userDataDir":"E:\\Code\\pocket-bot-v2\\browser-data"}
{"level":"info","message":"Browser initialized successfully","timestamp":"2025-05-28 18:09:58"}
{"level":"info","message":"WebSocket monitor injected","timestamp":"2025-05-28 18:09:58"}
{"level":"info","message":"Handling login process","timestamp":"2025-05-28 18:09:58"}
{"age":"0 minutes","isValid":true,"level":"info","message":"Found existing session","timestamp":"2025-05-28 18:09:58"}
{"age":"0 minutes","cookieCount":22,"level":"info","message":"Session loaded successfully","timestamp":"2025-05-28 18:10:04","url":"https://pocketoption.com/en/cabinet/"}
{"indicator":"[data-user]","level":"info","message":"User appears to be logged in","timestamp":"2025-05-28 18:10:07"}
{"level":"info","message":"Successfully restored session - already logged in","timestamp":"2025-05-28 18:10:07"}
{"level":"info","message":"Switching to demo account","timestamp":"2025-05-28 18:10:07"}
{"level":"info","message":"Already on demo account","timestamp":"2025-05-28 18:10:08"}
{"asset":"AUD/CAD OTC","level":"info","message":"Selecting asset","timestamp":"2025-05-28 18:10:08"}
{"asset":"AUD/CAD OTC","level":"warn","message":"Asset selector or option not found","timestamp":"2025-05-28 18:10:08"}
{"level":"info","message":"Selecting timeframe","timeframe":"M8","timestamp":"2025-05-28 18:10:08"}
{"level":"error","message":"Failed to select timeframe","stack":"Error: Node is either not clickable or not an Element\n    at CdpElementHandle.clickablePoint (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:673:23)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:250:32)\n    at async CdpElementHandle.click (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:703:30)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:253:36)\n    at async DOMController.selectTimeframe (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:166:5)\n    at async PocketOptionBot.setupTradingParameters (E:\\Code\\pocket-bot-v2\\src\\bot.js:73:4)\n    at async PocketOptionBot.initialize (E:\\Code\\pocket-bot-v2\\src\\bot.js:41:4)\n    at async main (E:\\Code\\pocket-bot-v2\\src\\bot.js:377:23)","timestamp":"2025-05-28 18:10:10"}
{"amount":1,"level":"info","message":"Setting trade amount","timestamp":"2025-05-28 18:10:10"}
{"level":"error","message":"Failed to set trade amount","stack":"Error: Node is either not clickable or not an Element\n    at CdpElementHandle.clickablePoint (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:673:23)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:250:32)\n    at async CdpElementHandle.click (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:703:30)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:253:36)\n    at async DOMController.setTradeAmount (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:194:5)\n    at async PocketOptionBot.setupTradingParameters (E:\\Code\\pocket-bot-v2\\src\\bot.js:76:4)\n    at async PocketOptionBot.initialize (E:\\Code\\pocket-bot-v2\\src\\bot.js:41:4)\n    at async main (E:\\Code\\pocket-bot-v2\\src\\bot.js:377:23)","timestamp":"2025-05-28 18:10:12"}
{"amount":1,"asset":"AUD/CAD OTC","level":"info","message":"Trading parameters set up","timeframe":"M8","timestamp":"2025-05-28 18:10:12"}
{"level":"info","message":"WebSocket URL discovered","timestamp":"2025-05-28 18:10:17","url":"wss://api-us-north.po.market:443/socket.io/?EIO=4&transport=websocket"}
{"level":"info","message":"Attempting to connect to WebSocket","timestamp":"2025-05-28 18:10:17","url":"wss://api-us-north.po.market:443/socket.io/?EIO=4&transport=websocket"}
{"level":"warn","message":"Cannot subscribe - WebSocket not connected","timestamp":"2025-05-28 18:10:17"}
{"level":"info","message":"WebSocket initialized and subscribed to market data","timestamp":"2025-05-28 18:10:17"}
{"level":"info","message":"Bot initialization completed successfully","timestamp":"2025-05-28 18:10:17"}
{"level":"info","message":"Starting trading bot","timestamp":"2025-05-28 18:10:17"}
{"balance":0,"level":"info","message":"Trading bot started successfully","timestamp":"2025-05-28 18:10:17"}
{"level":"info","message":"WebSocket connected successfully","timestamp":"2025-05-28 18:10:23"}
{"level":"info","message":"WebSocket connected","timestamp":"2025-05-28 18:10:23"}
{"activeTrades":0,"balance":0,"isRunning":true,"level":"info","message":"Bot status","runtime":32534,"timestamp":"2025-05-28 18:10:28"}
{"activeTrades":0,"balance":0,"isRunning":true,"level":"info","message":"Bot status","runtime":42530,"timestamp":"2025-05-28 18:10:38"}
{"activeTrades":0,"balance":0,"isRunning":true,"level":"info","message":"Bot status","runtime":53639,"timestamp":"2025-05-28 18:10:50"}
{"code":1005,"level":"warn","message":"WebSocket connection closed","reason":"","timestamp":"2025-05-28 18:10:54"}
{"level":"warn","message":"WebSocket disconnected","timestamp":"2025-05-28 18:10:54"}
{"level":"info","message":"Attempting to reconnect (1/10)","timestamp":"2025-05-28 18:10:54"}
{"activeTrades":0,"balance":0,"isRunning":true,"level":"info","message":"Bot status","runtime":62525,"timestamp":"2025-05-28 18:10:58"}
{"level":"info","message":"Attempting to connect to WebSocket","timestamp":"2025-05-28 18:10:59","url":"wss://pocketoption.com/ws/"}
{"level":"error","message":"WebSocket error","stack":"Error: Unexpected server response: 302\n    at ClientRequest.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\ws\\lib\\websocket.js:913:7)\n    at ClientRequest.emit (node:events:519:28)\n    at HTTPParser.parserOnIncomingClient (node:_http_client:702:27)\n    at HTTPParser.parserOnHeadersComplete (node:_http_common:118:17)\n    at TLSSocket.socketOnData (node:_http_client:544:22)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)\n    at TLSWrap.onStreamRead (node:internal/stream_base_commons:191:23)","timestamp":"2025-05-28 18:11:00"}
{"level":"info","message":"Initializing Pocket Option Trading Bot","timestamp":"2025-05-28 19:13:20"}
{"level":"info","message":"Initializing browser with session persistence","timestamp":"2025-05-28 19:13:20"}
{"level":"info","message":"Session persistence enabled","timestamp":"2025-05-28 19:13:20","userDataDir":"E:\\Code\\pocket-bot-v2\\browser-data"}
{"level":"info","message":"Browser initialized successfully","timestamp":"2025-05-28 19:13:22"}
{"level":"info","message":"WebSocket monitor injected","timestamp":"2025-05-28 19:13:22"}
{"level":"info","message":"Handling login process","timestamp":"2025-05-28 19:13:22"}
{"age":"63 minutes","isValid":true,"level":"info","message":"Found existing session","timestamp":"2025-05-28 19:13:22"}
{"age":"63 minutes","cookieCount":22,"level":"info","message":"Session loaded successfully","timestamp":"2025-05-28 19:13:31","url":"https://pocketoption.com/en/cabinet/"}
{"indicator":"[data-user]","level":"info","message":"User appears to be logged in","timestamp":"2025-05-28 19:13:34"}
{"level":"info","message":"Successfully restored session - already logged in","timestamp":"2025-05-28 19:13:34"}
{"level":"info","message":"Switching to demo account","timestamp":"2025-05-28 19:13:34"}
{"level":"info","message":"Already on demo account","timestamp":"2025-05-28 19:13:34"}
{"asset":"AUD/CAD OTC","level":"info","message":"Selecting asset","timestamp":"2025-05-28 19:13:34"}
{"asset":"AUD/CAD OTC","level":"warn","message":"Asset selector or option not found","timestamp":"2025-05-28 19:13:35"}
{"level":"info","message":"Selecting timeframe","timeframe":"M8","timestamp":"2025-05-28 19:13:35"}
{"level":"error","message":"Failed to select timeframe","stack":"Error: Node is either not clickable or not an Element\n    at CdpElementHandle.clickablePoint (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:673:23)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:250:32)\n    at async CdpElementHandle.click (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:703:30)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:253:36)\n    at async DOMController.selectTimeframe (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:166:5)\n    at async PocketOptionBot.setupTradingParameters (E:\\Code\\pocket-bot-v2\\src\\bot.js:73:4)\n    at async PocketOptionBot.initialize (E:\\Code\\pocket-bot-v2\\src\\bot.js:41:4)\n    at async main (E:\\Code\\pocket-bot-v2\\src\\bot.js:377:23)","timestamp":"2025-05-28 19:13:36"}
{"amount":1,"level":"info","message":"Setting trade amount","timestamp":"2025-05-28 19:13:36"}
{"level":"error","message":"Failed to set trade amount","stack":"Error: Node is either not clickable or not an Element\n    at CdpElementHandle.clickablePoint (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:673:23)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:250:32)\n    at async CdpElementHandle.click (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:703:30)\n    at async CdpElementHandle.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\ElementHandle.js:253:36)\n    at async DOMController.setTradeAmount (E:\\Code\\pocket-bot-v2\\src\\dom-controller.js:194:5)\n    at async PocketOptionBot.setupTradingParameters (E:\\Code\\pocket-bot-v2\\src\\bot.js:76:4)\n    at async PocketOptionBot.initialize (E:\\Code\\pocket-bot-v2\\src\\bot.js:41:4)\n    at async main (E:\\Code\\pocket-bot-v2\\src\\bot.js:377:23)","timestamp":"2025-05-28 19:13:38"}
{"amount":1,"asset":"AUD/CAD OTC","level":"info","message":"Trading parameters set up","timeframe":"M8","timestamp":"2025-05-28 19:13:38"}
{"level":"info","message":"WebSocket URL discovered","timestamp":"2025-05-28 19:13:43","url":"wss://api-us-north.po.market:443/socket.io/?EIO=4&transport=websocket"}
{"level":"info","message":"Attempting to connect to WebSocket","timestamp":"2025-05-28 19:13:43","url":"wss://api-us-north.po.market:443/socket.io/?EIO=4&transport=websocket"}
{"level":"warn","message":"Cannot subscribe - WebSocket not connected","timestamp":"2025-05-28 19:13:43"}
{"level":"info","message":"WebSocket initialized and subscribed to market data","timestamp":"2025-05-28 19:13:43"}
{"level":"info","message":"Bot initialization completed successfully","timestamp":"2025-05-28 19:13:43"}
{"level":"info","message":"Starting trading bot","timestamp":"2025-05-28 19:13:43"}
{"balance":0,"level":"info","message":"Trading bot started successfully","timestamp":"2025-05-28 19:13:44"}
{"level":"info","message":"WebSocket connected successfully","timestamp":"2025-05-28 19:13:50"}
{"level":"info","message":"WebSocket connected","timestamp":"2025-05-28 19:13:50"}
{"activeTrades":0,"balance":0,"isRunning":true,"level":"info","message":"Bot status","runtime":34645,"timestamp":"2025-05-28 19:13:55"}
{"activeTrades":0,"balance":0,"isRunning":true,"level":"info","message":"Bot status","runtime":44650,"timestamp":"2025-05-28 19:14:05"}
{"activeTrades":0,"balance":0,"isRunning":true,"level":"info","message":"Bot status","runtime":54654,"timestamp":"2025-05-28 19:14:15"}
{"code":1005,"level":"warn","message":"WebSocket connection closed","reason":"","timestamp":"2025-05-28 19:14:21"}
{"level":"warn","message":"WebSocket disconnected","timestamp":"2025-05-28 19:14:21"}
{"level":"info","message":"Attempting to reconnect (1/10)","timestamp":"2025-05-28 19:14:21"}
{"activeTrades":0,"balance":0,"isRunning":true,"level":"info","message":"Bot status","runtime":64636,"timestamp":"2025-05-28 19:14:25"}
{"level":"info","message":"Attempting to connect to WebSocket","timestamp":"2025-05-28 19:14:26","url":"wss://pocketoption.com/ws/"}
{"level":"error","message":"WebSocket error","stack":"Error: Unexpected server response: 302\n    at ClientRequest.<anonymous> (E:\\Code\\pocket-bot-v2\\node_modules\\ws\\lib\\websocket.js:913:7)\n    at ClientRequest.emit (node:events:519:28)\n    at HTTPParser.parserOnIncomingClient (node:_http_client:702:27)\n    at HTTPParser.parserOnHeadersComplete (node:_http_common:118:17)\n    at TLSSocket.socketOnData (node:_http_client:544:22)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)\n    at TLSWrap.onStreamRead (node:internal/stream_base_commons:191:23)","timestamp":"2025-05-28 19:14:27"}
